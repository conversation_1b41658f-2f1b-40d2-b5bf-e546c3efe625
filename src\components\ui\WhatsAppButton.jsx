import { FloatB<PERSON>on, Toolt<PERSON>, Badge } from 'antd';
import { WhatsAppOutlined, MessageOutlined } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { BRAND } from '../../utils/constants';

const WhatsAppButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      // Show tooltip after 3 seconds to grab attention
      setTimeout(() => setShowTooltip(true), 3000);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleWhatsAppClick = () => {
    const message = encodeURIComponent('Hi! I\'m interested in learning more about your digital marketing services.');
    const whatsappUrl = `https://wa.me/${BRAND.whatsapp.replace(/\D/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank');
    setShowTooltip(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ scale: 0, opacity: 0, y: 100 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0, opacity: 0, y: 100 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.2
          }}
          className="fixed bottom-6 right-6 z-50"
        >
          {/* Pulsing rings */}
          <motion.div
            className="absolute inset-0 rounded-full"
            style={{
              background: 'rgba(37, 211, 102, 0.2)',
              width: '80px',
              height: '80px',
              left: '-10px',
              top: '-10px'
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.1, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          <motion.div
            className="absolute inset-0 rounded-full"
            style={{
              background: 'rgba(37, 211, 102, 0.3)',
              width: '70px',
              height: '70px',
              left: '-5px',
              top: '-5px'
            }}
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.4, 0.2, 0.4]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5
            }}
          />

          {/* Notification badge */}
          <AnimatePresence>
            {showTooltip && (
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                className="absolute -top-2 -left-2"
              >
                <Badge
                  count="💬"
                  style={{
                    backgroundColor: '#ff4d4f',
                    fontSize: '12px',
                    minWidth: '24px',
                    height: '24px',
                    lineHeight: '24px',
                    borderRadius: '12px',
                    border: '2px solid white',
                    boxShadow: '0 4px 12px rgba(255, 77, 79, 0.3)'
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Tooltip message */}
          <AnimatePresence>
            {showTooltip && (
              <motion.div
                initial={{ opacity: 0, x: 20, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 20, scale: 0.8 }}
                className="absolute right-full mr-4 top-1/2 transform -translate-y-1/2 bg-white rounded-xl shadow-xl p-3 border border-gray-200"
                style={{ minWidth: '200px' }}
              >
                <div className="text-sm font-medium text-gray-800 mb-1">
                  💬 Need help?
                </div>
                <div className="text-xs text-gray-600">
                  Chat with us on WhatsApp!
                </div>

                {/* Arrow */}
                <div
                  className="absolute top-1/2 -right-2 transform -translate-y-1/2 w-0 h-0"
                  style={{
                    borderLeft: '8px solid white',
                    borderTop: '8px solid transparent',
                    borderBottom: '8px solid transparent'
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main button */}
          <Tooltip title="Chat with us on WhatsApp!" placement="left">
            <motion.div
              whileHover={{
                scale: 1.1,
                rotate: [0, -5, 5, 0],
                boxShadow: '0 12px 32px rgba(37, 211, 102, 0.5)'
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <FloatButton
                icon={
                  <motion.div
                    animate={{
                      rotate: [0, 10, -10, 0]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <WhatsAppOutlined />
                  </motion.div>
                }
                onClick={handleWhatsAppClick}
                style={{
                  backgroundColor: '#25D366',
                  width: '60px',
                  height: '60px',
                  fontSize: '24px',
                  boxShadow: '0 8px 24px rgba(37, 211, 102, 0.4)',
                  border: '3px solid white',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              />
            </motion.div>
          </Tooltip>

          {/* Shine effect */}
          <motion.div
            className="absolute inset-0 rounded-full pointer-events-none"
            style={{
              background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%)',
              width: '60px',
              height: '60px'
            }}
            animate={{
              rotate: 360
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WhatsAppButton;
