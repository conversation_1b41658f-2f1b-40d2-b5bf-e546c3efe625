import { FloatButton, Tooltip } from 'antd';
import { WhatsAppOutlined } from '@ant-design/icons';
import { BRAND } from '../../utils/constants';

const WhatsAppButton = () => {
  const handleWhatsAppClick = () => {
    const message = encodeURIComponent('Hi! I\'m interested in learning more about your digital marketing services.');
    const whatsappUrl = `https://wa.me/${BRAND.whatsapp.replace(/\D/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <Tooltip title="Chat with us on WhatsApp!" placement="left">
      <FloatButton
        icon={<WhatsAppOutlined />}
        onClick={handleWhatsAppClick}
        style={{
          backgroundColor: '#25D366',
          width: '60px',
          height: '60px',
          fontSize: '24px',
          boxShadow: '0 8px 24px rgba(37, 211, 102, 0.4)',
          border: 'none'
        }}
        className="hover:scale-110 transition-transform duration-300"
      />
    </Tooltip>
  );
};

export default WhatsAppButton;
