import { motion } from 'framer-motion';
import ModernNavbar from './ModernNavbar';
import FloatingWhatsApp from '../common/FloatingWhatsApp';
import Footer from './Footer';

const ModernLayout = ({ children }) => {
  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      <ModernNavbar />
      
      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative"
      >
        {children}
      </motion.main>
      
      <Footer />
      <FloatingWhatsApp />
    </div>
  );
};

export default ModernLayout;
