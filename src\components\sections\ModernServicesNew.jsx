import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { Link } from 'react-router-dom';
import {
  MagnifyingGlassIcon,
  DevicePhoneMobileIcon,
  ChartBarIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  PaintBrushIcon,
  ArrowRightIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const ModernServicesNew = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const services = [
    {
      icon: MagnifyingGlassIcon,
      title: 'Search Engine Optimization',
      description: 'Dominate search results with our proven SEO strategies that drive organic traffic and boost rankings.',
      features: ['Keyword Research', 'On-Page SEO', 'Link Building', 'Technical SEO'],
      gradient: 'from-blue-500 to-cyan-500',
      delay: 0.1
    },
    {
      icon: ChartBarIcon,
      title: 'Pay-Per-Click Advertising',
      description: 'Maximize ROI with targeted PPC campaigns across Google Ads, Facebook, and other platforms.',
      features: ['Google Ads', 'Facebook Ads', 'Campaign Optimization', 'A/B Testing'],
      gradient: 'from-purple-500 to-pink-500',
      delay: 0.2
    },
    {
      icon: DevicePhoneMobileIcon,
      title: 'Social Media Marketing',
      description: 'Build brand awareness and engage your audience across all major social media platforms.',
      features: ['Content Strategy', 'Community Management', 'Influencer Marketing', 'Analytics'],
      gradient: 'from-green-500 to-emerald-500',
      delay: 0.3
    },
    {
      icon: EnvelopeIcon,
      title: 'Email Marketing',
      description: 'Nurture leads and retain customers with personalized email campaigns that convert.',
      features: ['Campaign Design', 'Automation', 'Segmentation', 'Performance Tracking'],
      gradient: 'from-orange-500 to-red-500',
      delay: 0.4
    },
    {
      icon: GlobeAltIcon,
      title: 'Web Development',
      description: 'Create stunning, high-converting websites that provide exceptional user experiences.',
      features: ['Responsive Design', 'E-commerce', 'CMS Integration', 'Performance Optimization'],
      gradient: 'from-indigo-500 to-blue-500',
      delay: 0.5
    },
    {
      icon: PaintBrushIcon,
      title: 'Brand Design',
      description: 'Develop compelling brand identities that resonate with your target audience.',
      features: ['Logo Design', 'Brand Guidelines', 'Marketing Materials', 'Brand Strategy'],
      gradient: 'from-pink-500 to-rose-500',
      delay: 0.6
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  };

  return (
    <section ref={ref} className="section-modern bg-slate-50 dark:bg-slate-800/50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-400/10 to-cyan-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="container-modern relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 rounded-full glass border border-white/20 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6"
            >
              <StarIcon className="w-4 h-4 mr-2" />
              Our Services
            </motion.div>

            <motion.h2
              variants={itemVariants}
              className="text-display font-black mb-6"
            >
              <span className="text-gradient-primary">Digital Marketing</span>
              <br />
              <span className="text-slate-900 dark:text-white">Services That Drive</span>
              <br />
              <span className="text-gradient-secondary">Real Results</span>
            </motion.h2>

            <motion.p
              variants={itemVariants}
              className="text-body-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto"
            >
              We offer comprehensive digital marketing solutions designed to help your business grow online, 
              reach your target audience, and achieve measurable results that matter.
            </motion.p>
          </motion.div>

          {/* Services Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
          >
            {services.map((service, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group"
              >
                <div className="card-elevated p-8 h-full hover:scale-105 transition-all duration-300 relative overflow-hidden">
                  {/* Gradient Background */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                  
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-br ${service.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <service.icon className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-heading font-bold text-slate-900 dark:text-white mb-4 group-hover:text-gradient-primary transition-all duration-300">
                    {service.title}
                  </h3>
                  
                  <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                        <CheckCircleIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* Learn More Link */}
                  <Link 
                    to="/services"
                    className="inline-flex items-center text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300 transition-colors group/link"
                  >
                    Learn More
                    <ArrowRightIcon className="w-4 h-4 ml-2 group-hover/link:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Section */}
          <motion.div
            variants={itemVariants}
            className="text-center"
          >
            <div className="card-elevated p-12 max-w-4xl mx-auto">
              <h3 className="text-heading font-bold text-slate-900 dark:text-white mb-4">
                Ready to Transform Your Business?
              </h3>
              <p className="text-body-lg text-slate-600 dark:text-slate-300 mb-8">
                Let's discuss how our digital marketing services can help you achieve your business goals.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/contact">
                  <motion.button
                    className="btn-gradient px-8 py-4 text-lg font-bold rounded-xl shadow-xl"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Get Started Today
                  </motion.button>
                </Link>
                <Link to="/case-studies">
                  <motion.button
                    className="btn-outline px-8 py-4 text-lg font-bold rounded-xl"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    View Our Work
                  </motion.button>
                </Link>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernServicesNew;
