import { useEffect } from 'react';
import ModernHeroNew from '../components/sections/ModernHeroNew';
import ModernServicesNew from '../components/sections/ModernServicesNew';
import ModernAboutNew from '../components/sections/ModernAboutNew';
import Testimonials from '../components/sections/Testimonials';
import ClientLogos from '../components/sections/ClientLogos';
import CTA from '../components/sections/CTA';

const Home = () => {
  useEffect(() => {
    document.title = 'Brandify - Empowering Brands Digitally | Digital Marketing Agency';
    document.querySelector('meta[name="description"]')?.setAttribute('content', 
      'Brandify is a leading digital marketing agency helping businesses grow online through SEO, social media marketing, PPC advertising, and web development services.'
    );
  }, []);

  return (
    <div>
      <ModernHeroNew />
      <ClientLogos />
      <ModernServicesNew />
      <ModernAboutNew />
      <Testimonials />
      <CTA />
    </div>
  );
};

export default Home;
