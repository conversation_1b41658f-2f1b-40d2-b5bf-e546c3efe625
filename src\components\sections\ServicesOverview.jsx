import { Link } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import {
  MagnifyingGlassIcon,
  ShareIcon,
  TargetIcon,
  PaintBrushIcon,
  ComputerDesktopIcon,
  PencilIcon,
  ArrowRightIcon,
  RocketLaunchIcon,
  StarIcon,
  CheckCircleIcon,
  TrophyIcon,
  BoltIcon,
  FireIcon,
  ChartBarIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import { SERVICES } from '../../utils/constants';

const ServicesOverview = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  const getIcon = (iconName) => {
    const icons = {
      Search: SearchOutlined,
      Share2: ShareAltOutlined,
      Target: AimOutlined,
      Palette: BgColorsOutlined,
      Monitor: DesktopOutlined,
      PenTool: EditOutlined
    };
    const IconComponent = icons[iconName];
    return IconComponent ? <IconComponent style={{ fontSize: '32px' }} /> : <SearchOutlined style={{ fontSize: '32px' }} />;
  };

  const serviceColors = [
    { primary: '#0057ff', secondary: '#1890ff', bg: '#eff6ff' },
    { primary: '#10b981', secondary: '#34d399', bg: '#ecfdf5' },
    { primary: '#f59e0b', secondary: '#fbbf24', bg: '#fffbeb' },
    { primary: '#ef4444', secondary: '#f87171', bg: '#fef2f2' },
    { primary: '#8b5cf6', secondary: '#a78bfa', bg: '#f5f3ff' },
    { primary: '#06b6d4', secondary: '#22d3ee', bg: '#ecfeff' }
  ];

  return (
    <section
      ref={ref}
      className="py-20 bg-white dark:bg-gray-900 relative overflow-hidden"
      id="services"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-1/3 w-72 h-72 bg-gradient-to-r from-green-400/10 to-cyan-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px]" />

      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Modern Section Header */}
          <motion.div
            variants={itemVariants}
            className="text-center mb-20"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 rounded-full glass border border-white/20 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6"
            >
              <StarIcon className="w-4 h-4 mr-2" />
              Premium Digital Services
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-4xl md:text-5xl lg:text-6xl font-black leading-tight mb-6"
            >
              <span className="text-gradient-primary">
                Our Digital Marketing
              </span>
              <br />
              <span className="text-gray-900 dark:text-gray-100">
                Services
              </span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8"
            >
              We offer{' '}
              <motion.span
                className="font-semibold text-gradient-primary"
                whileHover={{ scale: 1.05 }}
              >
                comprehensive digital marketing solutions
              </motion.span>
              {' '}to help your business grow online and reach your target audience with{' '}
              <motion.span
                className="font-semibold text-gradient-accent"
                whileHover={{ scale: 1.05 }}
              >
                proven strategies
              </motion.span>
              {' '}that deliver measurable results.
            </motion.p>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="flex flex-wrap justify-center items-center gap-6 mt-12"
            >
              {[
                { icon: CheckCircleIcon, text: "500+ Projects", color: "text-green-500" },
                { icon: BoltIcon, text: "98% Success Rate", color: "text-yellow-500" },
                { icon: FireIcon, text: "5+ Years Experience", color: "text-red-500" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="flex items-center gap-3 px-6 py-3 glass-card rounded-2xl shadow-modern border border-white/20 dark:border-gray-700/20"
                  whileHover={{
                    scale: 1.05,
                    y: -2
                  }}
                >
                  <stat.icon className={`w-5 h-5 ${stat.color}`} />
                  <span className="font-semibold text-gray-700 dark:text-gray-300">{stat.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Enhanced Services Grid */}
          <Row gutter={[32, 32]} className="mb-16">
            {SERVICES.map((service, index) => {
              const colors = serviceColors[index % serviceColors.length];
              return (
                <Col xs={24} md={12} lg={8} key={service.id}>
                  <motion.div
                    variants={itemVariants}
                    whileHover={{ y: -8 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Tilt
                      tiltMaxAngleX={5}
                      tiltMaxAngleY={5}
                      perspective={1000}
                      scale={1.02}
                      transitionSpeed={1000}
                    >
                      <Card
                        className="card-hover h-full border-0 relative overflow-hidden"
                        style={{
                          borderRadius: '24px',
                          background: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                        bodyStyle={{ padding: '40px 32px', textAlign: 'center' }}
                      >
                        {/* Animated background gradient */}
                        <motion.div
                          className="absolute inset-0 opacity-0 pointer-events-none"
                          style={{
                            background: `linear-gradient(135deg, ${colors.primary}15 0%, ${colors.secondary}15 100%)`
                          }}
                          whileHover={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        />

                        {/* Service Badge */}
                        <motion.div
                          className="absolute top-4 right-4"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: index * 0.1 + 0.5 }}
                        >
                          <Badge
                            count={`0${index + 1}`}
                            style={{
                              backgroundColor: colors.primary,
                              color: 'white',
                              fontWeight: 'bold',
                              fontSize: '12px',
                              minWidth: '24px',
                              height: '24px',
                              lineHeight: '24px',
                              borderRadius: '12px'
                            }}
                          />
                        </motion.div>

                        {/* Enhanced Icon */}
                        <motion.div
                          className="relative mb-6"
                          whileHover={{
                            rotate: [0, -5, 5, 0],
                            scale: 1.1
                          }}
                          transition={{ duration: 0.5 }}
                        >
                          <div
                            className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto relative overflow-hidden"
                            style={{
                              background: `linear-gradient(135deg, ${colors.bg} 0%, ${colors.primary}20 100%)`,
                              color: colors.primary,
                              border: `2px solid ${colors.primary}20`
                            }}
                          >
                            <span className="relative z-10 text-3xl">
                              {getIcon(service.icon)}
                            </span>

                            {/* Animated shine effect */}
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
                              animate={{
                                x: ['-100%', '100%']
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                repeatDelay: 3
                              }}
                            />
                          </div>

                          {/* Floating particles */}
                          {[...Array(3)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-1 h-1 rounded-full"
                              style={{
                                background: colors.primary,
                                top: `${20 + i * 20}%`,
                                right: `${10 + i * 15}%`,
                              }}
                              animate={{
                                opacity: [0, 1, 0],
                                scale: [0, 1, 0],
                                y: [0, -10, 0],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                delay: i * 0.5,
                              }}
                            />
                          ))}
                        </motion.div>

                        {/* Title */}
                        <Title
                          level={4}
                          className="mb-4 relative z-10"
                          style={{
                            color: '#1a202c',
                            fontWeight: 700
                          }}
                        >
                          {service.title}
                        </Title>

                        {/* Description */}
                        <Paragraph
                          className="mb-6 text-gray-600 leading-relaxed relative z-10"
                          style={{ fontSize: '16px' }}
                        >
                          {service.description}
                        </Paragraph>

                        {/* Enhanced Benefits */}
                        <div className="mb-6 relative z-10">
                          {service.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                            <motion.div
                              key={benefitIndex}
                              className="flex items-center gap-3 py-2"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 + benefitIndex * 0.1 }}
                            >
                              <div
                                className="w-2 h-2 rounded-full flex-shrink-0"
                                style={{ background: colors.primary }}
                              />
                              <span className="text-gray-600 text-sm font-medium">
                                {benefit}
                              </span>
                            </motion.div>
                          ))}
                        </div>

                        {/* Progress indicator */}
                        <motion.div
                          className="mb-6 relative z-10"
                          initial={{ width: 0 }}
                          animate={{ width: '100%' }}
                          transition={{ delay: index * 0.1 + 1, duration: 1 }}
                        >
                          <Progress
                            percent={85 + Math.floor(Math.random() * 15)}
                            size="small"
                            strokeColor={{
                              '0%': colors.primary,
                              '100%': colors.secondary,
                            }}
                            trailColor={colors.bg}
                            showInfo={false}
                          />
                          <div className="text-xs text-gray-500 mt-1">Success Rate</div>
                        </motion.div>

                        {/* Enhanced CTA Button */}
                        <Link to="/services">
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Button
                              type="primary"
                              icon={<ArrowRightOutlined />}
                              className="relative z-10"
                              style={{
                                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`,
                                border: 'none',
                                borderRadius: '12px',
                                height: '44px',
                                fontWeight: 600,
                                fontSize: '14px',
                                boxShadow: `0 4px 16px ${colors.primary}30`
                              }}
                            >
                              Learn More
                            </Button>
                          </motion.div>
                        </Link>
                      </Card>
                    </Tilt>
                  </motion.div>
                </Col>
              );
            })}
          </Row>

          {/* Enhanced CTA Section */}
          <motion.div variants={itemVariants}>
            <Tilt
              tiltMaxAngleX={2}
              tiltMaxAngleY={2}
              perspective={1000}
              scale={1.01}
            >
              <Card
                className="border-0 relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 50%, #40a9ff 100%)',
                  borderRadius: '32px',
                  textAlign: 'center'
                }}
                bodyStyle={{ padding: '64px 48px' }}
              >
                {/* Animated background elements */}
                <div className="absolute inset-0 pointer-events-none">
                  {/* Floating orbs */}
                  <motion.div
                    className="absolute top-10 left-10 w-20 h-20 rounded-full opacity-20"
                    style={{ background: 'rgba(255, 255, 255, 0.3)' }}
                    animate={{
                      y: [0, -20, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />

                  <motion.div
                    className="absolute bottom-10 right-10 w-16 h-16 rounded-full opacity-20"
                    style={{ background: 'rgba(255, 255, 255, 0.3)' }}
                    animate={{
                      y: [0, 15, 0],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  />

                  {/* Sparkles */}
                  {[...Array(8)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full opacity-60"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                      }}
                      animate={{
                        opacity: [0.6, 1, 0.6],
                        scale: [1, 1.5, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: Math.random() * 2,
                      }}
                    />
                  ))}
                </div>

                <div className="relative z-10">
                  {/* Icon */}
                  <motion.div
                    className="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-6"
                    style={{
                      background: 'rgba(255, 255, 255, 0.2)',
                      backdropFilter: 'blur(10px)'
                    }}
                    whileHover={{
                      scale: 1.1,
                      rotate: 360
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    <RocketOutlined className="text-white text-2xl" />
                  </motion.div>

                  <Title
                    level={2}
                    className="text-white mb-4"
                    style={{ fontSize: 'clamp(1.8rem, 4vw, 2.5rem)' }}
                  >
                    Ready to Transform Your Digital Presence?
                  </Title>

                  <Paragraph
                    className="text-white/90 mb-8 max-w-2xl mx-auto"
                    style={{
                      fontSize: '20px',
                      lineHeight: 1.6
                    }}
                  >
                    Let's discuss how our{' '}
                    <span className="font-semibold text-yellow-300">proven strategies</span>
                    {' '}can help your business achieve its digital marketing goals and{' '}
                    <span className="font-semibold text-green-300">boost revenue by 300%+</span>.
                  </Paragraph>

                  <Space size="large" wrap className="justify-center">
                    {/* Primary CTA */}
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link to="/contact">
                        <Button
                          size="large"
                          icon={<RocketOutlined />}
                          style={{
                            background: 'white',
                            color: '#0057ff',
                            border: 'none',
                            borderRadius: '16px',
                            height: '56px',
                            padding: '0 40px',
                            fontWeight: 700,
                            fontSize: '18px',
                            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.2)'
                          }}
                        >
                          Get Free Consultation
                        </Button>
                      </Link>
                    </motion.div>

                    {/* Secondary CTA */}
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link to="/services">
                        <Button
                          size="large"
                          className="glass"
                          style={{
                            background: 'rgba(255, 255, 255, 0.1)',
                            color: 'white',
                            border: '2px solid rgba(255, 255, 255, 0.3)',
                            borderRadius: '16px',
                            height: '56px',
                            padding: '0 40px',
                            fontWeight: 600,
                            fontSize: '18px',
                            backdropFilter: 'blur(10px)'
                          }}
                        >
                          View All Services
                        </Button>
                      </Link>
                    </motion.div>
                  </Space>

                  {/* Trust indicators */}
                  <motion.div
                    className="flex justify-center items-center gap-8 mt-8 text-white/80"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 }}
                  >
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircleOutlined className="text-green-300" />
                      <span>Free Strategy Session</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircleOutlined className="text-green-300" />
                      <span>No Long-term Contracts</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircleOutlined className="text-green-300" />
                      <span>Results in 30 Days</span>
                    </div>
                  </motion.div>
                </div>
              </Card>
            </Tilt>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesOverview;
