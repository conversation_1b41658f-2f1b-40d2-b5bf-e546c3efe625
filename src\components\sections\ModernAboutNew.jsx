import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { Link } from 'react-router-dom';
import {
  TrophyIcon,
  UsersIcon,
  TargetIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  StarIcon,
  ArrowRightIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const ModernAboutNew = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const features = [
    {
      icon: TrophyIcon,
      title: 'Proven Results',
      description: 'Track record of delivering measurable growth for our clients with 300% average ROI increase.',
      gradient: 'from-yellow-500 to-orange-500',
      stat: '300%',
      statLabel: 'Avg ROI Increase'
    },
    {
      icon: UsersIcon,
      title: 'Expert Team',
      description: 'Certified professionals with years of industry experience and cutting-edge expertise.',
      gradient: 'from-green-500 to-emerald-500',
      stat: '50+',
      statLabel: 'Team Members'
    },
    {
      icon: TargetIcon,
      title: 'Strategic Approach',
      description: 'Data-driven strategies tailored to your business goals and target audience.',
      gradient: 'from-blue-500 to-purple-500',
      stat: '98%',
      statLabel: 'Success Rate'
    }
  ];

  const stats = [
    { number: '500+', label: 'Projects Completed' },
    { number: '98%', label: 'Client Satisfaction' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Support Available' }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  };

  return (
    <section ref={ref} className="section-modern bg-white dark:bg-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-blue-400/10 to-cyan-400/10 rounded-full blur-3xl"
          animate={{
            y: [0, 20, 0],
            x: [0, -15, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      <div className="container-modern relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 rounded-full glass border border-white/20 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6"
            >
              <HeartIcon className="w-4 h-4 mr-2" />
              About Brandify
            </motion.div>

            <motion.h2
              variants={itemVariants}
              className="text-display font-black mb-6"
            >
              <span className="text-slate-900 dark:text-white">We're</span>
              <br />
              <span className="text-gradient-primary">Digital Marketing</span>
              <br />
              <span className="text-gradient-secondary">Experts</span>
            </motion.h2>

            <motion.p
              variants={itemVariants}
              className="text-body-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto"
            >
              At Brandify, we're passionate about helping businesses thrive in the digital landscape. 
              Our team of experts combines creativity, strategy, and cutting-edge technology to deliver 
              exceptional results that drive real business growth.
            </motion.p>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group"
              >
                <div className="card-elevated p-8 text-center hover:scale-105 transition-all duration-300 relative overflow-hidden">
                  {/* Gradient Background */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                  
                  {/* Icon */}
                  <div className={`w-20 h-20 bg-gradient-to-br ${feature.gradient} rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-10 h-10 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-subheading font-bold text-slate-900 dark:text-white mb-4">
                    {feature.title}
                  </h3>
                  
                  <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Stat */}
                  <div className="border-t border-slate-200 dark:border-slate-700 pt-6">
                    <div className={`text-3xl font-black bg-gradient-to-br ${feature.gradient} bg-clip-text text-transparent mb-2`}>
                      {feature.stat}
                    </div>
                    <div className="text-sm text-slate-500 dark:text-slate-400 font-medium">
                      {feature.statLabel}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Stats Section */}
          <motion.div
            variants={itemVariants}
            className="card-elevated p-12 mb-20"
          >
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : {}}
                  transition={{ delay: 0.8 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-4xl font-black text-gradient-primary mb-2">
                    {stat.number}
                  </div>
                  <div className="text-slate-600 dark:text-slate-400 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Mission Statement */}
          <motion.div
            variants={itemVariants}
            className="text-center max-w-4xl mx-auto mb-16"
          >
            <h3 className="text-heading font-bold text-slate-900 dark:text-white mb-6">
              Our Mission
            </h3>
            <p className="text-body-lg text-slate-600 dark:text-slate-300 leading-relaxed mb-8">
              To empower businesses of all sizes to achieve their full potential through innovative 
              digital marketing strategies. We believe in building long-term partnerships based on 
              trust, transparency, and measurable results.
            </p>
            
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              {['Innovation', 'Transparency', 'Results-Driven', 'Client-Focused'].map((value, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : {}}
                  transition={{ delay: 1.2 + index * 0.1 }}
                  className="px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full border border-blue-200 dark:border-blue-700"
                >
                  <span className="text-blue-700 dark:text-blue-300 font-semibold">{value}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            variants={itemVariants}
            className="text-center"
          >
            <div className="card-elevated p-12 max-w-4xl mx-auto">
              <h3 className="text-heading font-bold text-slate-900 dark:text-white mb-4">
                Ready to Work with Us?
              </h3>
              <p className="text-body-lg text-slate-600 dark:text-slate-300 mb-8">
                Let's discuss how we can help your business achieve its digital marketing goals.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/contact">
                  <motion.button
                    className="btn-gradient px-8 py-4 text-lg font-bold rounded-xl shadow-xl flex items-center gap-3"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <RocketLaunchIcon className="w-5 h-5" />
                    Get Started Today
                  </motion.button>
                </Link>
                <Link to="/about">
                  <motion.button
                    className="btn-outline px-8 py-4 text-lg font-bold rounded-xl flex items-center gap-3"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Learn More About Us
                    <ArrowRightIcon className="w-5 h-5" />
                  </motion.button>
                </Link>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernAboutNew;
