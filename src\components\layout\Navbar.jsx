import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { <PERSON><PERSON>, Drawer, Badge, Dropdown } from 'antd';
import {
  MenuOutlined,
  PhoneOutlined,
  RocketOutlined,
  DownOutlined,
  MailOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { NAV_LINKS, BRAND } from '../../utils/constants';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  const navItems = [
    { path: '/', label: 'Home', icon: '🏠' },
    { path: '/about', label: 'About', icon: '👥' },
    { 
      path: '/services', 
      label: 'Services', 
      icon: '⚡', 
      badge: 'Hot',
      dropdown: [
        { label: 'SEO & Marketing', path: '/services', icon: '📈' },
        { label: 'Web Development', path: '/services', icon: '💻' },
        { label: 'Social Media', path: '/services', icon: '📱' },
        { label: 'PPC Advertising', path: '/services', icon: '🎯' }
      ]
    },
    { path: '/case-studies', label: 'Portfolio', icon: '🏆' },
    { path: '/blog', label: 'Blog', icon: '📝', badge: 'New' },
    { path: '/contact', label: 'Contact', icon: '💬' }
  ];

  return (
    <motion.nav
      className="fixed top-0 left-0 right-0 z-50 w-full"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div
        className={`w-full transition-all duration-500 ${
          isScrolled 
            ? 'bg-white/98 backdrop-blur-xl shadow-xl' 
            : 'bg-white shadow-md'
        }`}
        style={{
          borderBottom: 'none',
          boxShadow: isScrolled 
            ? '0 4px 20px rgba(0, 0, 0, 0.08)' 
            : '0 2px 10px rgba(0, 0, 0, 0.05)'
        }}
      >
        {/* Desktop Navigation */}
        <div className="hidden lg:block w-full">
          <div className="w-full px-6 xl:px-8">
            <div className="flex items-center h-24 navbar-container">

              {/* Logo Section - Fixed Width */}
              <div className="flex items-center w-80">
                <motion.div
                  className="flex items-center"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link to="/" className="flex items-center gap-4" onClick={closeMenu}>
                    <div className="relative">
                      <motion.div
                        className="w-12 h-12 rounded-xl flex items-center justify-center relative overflow-hidden"
                        style={{
                          background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                          boxShadow: '0 6px 20px rgba(30, 64, 175, 0.25)'
                        }}
                        whileHover={{
                          boxShadow: '0 8px 25px rgba(30, 64, 175, 0.35)',
                          y: -1
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <span className="text-white font-bold text-lg relative z-10">B</span>

                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0"
                          whileHover={{
                            opacity: 0.15,
                            x: ['-100%', '100%']
                          }}
                          transition={{ duration: 0.6 }}
                        />
                      </motion.div>

                      <motion.div
                        className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                        animate={{
                          scale: [1, 1.2, 1],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                        }}
                      />
                    </div>

                    <div className="flex flex-col">
                      <motion.span
                        className="text-xl font-bold text-gray-900"
                        whileHover={{ scale: 1.02 }}
                      >
                        {BRAND.name}
                      </motion.span>
                      <span className="text-xs text-gray-500 font-medium -mt-0.5">
                        Digital Excellence
                      </span>
                    </div>
                  </Link>
                </motion.div>
              </div>

              {/* Center Navigation */}
              <div className="flex items-center justify-center flex-1 px-8">
                <div className="flex items-center gap-2">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.path}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                      whileHover={{ y: -1 }}
                    >
                      {item.dropdown ? (
                        <Dropdown
                          menu={{
                            items: item.dropdown.map((dropItem, idx) => ({
                              key: idx,
                              label: (
                                <Link to={dropItem.path} className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                  <span className="text-lg">{dropItem.icon}</span>
                                  <div>
                                    <div className="font-semibold text-gray-800">{dropItem.label}</div>
                                    <div className="text-xs text-gray-500">Professional services</div>
                                  </div>
                                </Link>
                              ),
                            }))
                          }}
                          trigger={['hover']}
                          placement="bottomCenter"
                        >
                          <Link
                            to={item.path}
                            className={`relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 group ${
                              location.pathname === item.path
                                ? 'bg-blue-600 text-white shadow-md'
                                : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                            }`}
                          >
                            <span className="text-sm">{item.icon}</span>
                            <span className="text-sm font-semibold">{item.label}</span>
                            <DownOutlined className="text-xs opacity-60" />
                            {item.badge && (
                              <Badge
                                count={item.badge}
                                style={{
                                  backgroundColor: '#ef4444',
                                  fontSize: '8px',
                                  height: '14px',
                                  lineHeight: '14px',
                                  minWidth: '14px'
                                }}
                              />
                            )}
                          </Link>
                        </Dropdown>
                      ) : (
                        <Link
                          to={item.path}
                          className={`relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 group ${
                            location.pathname === item.path
                              ? 'bg-blue-600 text-white shadow-md'
                              : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                          }`}
                        >
                          <span className="text-sm">{item.icon}</span>
                          <span className="text-sm font-semibold">{item.label}</span>
                          {item.badge && (
                            <Badge
                              count={item.badge}
                              style={{
                                backgroundColor: item.badge === 'Hot' ? '#ef4444' : '#10b981',
                                fontSize: '8px',
                                height: '14px',
                                lineHeight: '14px',
                                minWidth: '14px'
                              }}
                            />
                          )}
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Right Action Buttons - Fixed Width */}
              <div className="flex items-center justify-end gap-4 w-80">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <a href={`tel:${BRAND.phone}`}>
                    <Button
                      type="text"
                      icon={<PhoneOutlined className="text-base" />}
                      className="flex items-center gap-2 px-4 py-2 h-10 rounded-lg font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300"
                    >
                      <span className="text-sm font-semibold">Call Now</span>
                    </Button>
                  </a>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link to="/contact">
                    <Button
                      type="primary"
                      icon={<RocketOutlined className="text-base" />}
                      className="h-10 px-5 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
                      style={{
                        background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                        border: 'none',
                        fontSize: '14px'
                      }}
                    >
                      Get Started
                    </Button>
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden w-full">
          <div className="px-4 sm:px-6">
            <div className="flex items-center justify-between h-16">

              <motion.div
                className="flex items-center"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Link to="/" className="flex items-center gap-3" onClick={closeMenu}>
                  <div className="relative">
                    <motion.div
                      className="w-9 h-9 rounded-lg flex items-center justify-center relative overflow-hidden"
                      style={{
                        background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                        boxShadow: '0 4px 15px rgba(30, 64, 175, 0.3)'
                      }}
                    >
                      <span className="text-white font-bold text-base relative z-10">B</span>
                    </motion.div>

                    <motion.div
                      className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-white"
                      animate={{
                        scale: [1, 1.2, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                      }}
                    />
                  </div>

                  <div className="flex flex-col">
                    <span className="text-base font-bold text-gray-900">
                      {BRAND.name}
                    </span>
                    <span className="text-xs text-gray-500 font-medium -mt-0.5">
                      Digital Excellence
                    </span>
                  </div>
                </Link>
              </motion.div>

              <div className="flex items-center gap-3">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Link to="/contact">
                    <Button
                      type="primary"
                      size="small"
                      className="h-8 px-3 rounded-lg font-semibold text-xs"
                      style={{
                        background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                        border: 'none'
                      }}
                    >
                      Get Started
                    </Button>
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    type="text"
                    onClick={toggleMenu}
                    className="w-9 h-9 rounded-lg flex items-center justify-center hover:bg-gray-100 transition-all duration-300"
                    style={{
                      color: isOpen ? '#1e40af' : '#374151',
                      background: isOpen ? 'rgba(30, 64, 175, 0.1)' : 'transparent'
                    }}
                  >
                    <motion.div
                      animate={{ rotate: isOpen ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {isOpen ? <CloseOutlined className="text-sm" /> : <MenuOutlined className="text-sm" />}
                    </motion.div>
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Drawer */}
      <Drawer
        title={
          <div className="flex items-center gap-3">
            <div
              className="w-10 h-10 rounded-xl flex items-center justify-center"
              style={{
                background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                boxShadow: '0 6px 20px rgba(30, 64, 175, 0.3)'
              }}
            >
              <span className="text-white font-bold text-lg">B</span>
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900">{BRAND.name}</div>
              <div className="text-sm text-gray-500">Digital Excellence</div>
            </div>
          </div>
        }
        placement="right"
        onClose={closeMenu}
        open={isOpen}
        width={320}
        styles={{
          body: {
            padding: 0,
            background: 'linear-gradient(135deg, #ffffff 0%, #f9fafb 100%)'
          },
          header: {
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            padding: '16px 20px'
          }
        }}
        closeIcon={<CloseOutlined className="text-gray-500 hover:text-gray-700" />}
      >
        <div className="flex flex-col h-full">
          <div className="flex-1 p-5">
            <div className="space-y-1 mb-6">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    to={item.path}
                    onClick={closeMenu}
                    className={`flex items-center gap-3 px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 ${
                      location.pathname === item.path
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                    }`}
                  >
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-lg ${
                      location.pathname === item.path
                        ? 'bg-white/20 text-white'
                        : 'bg-gray-100 text-gray-500'
                    }`}>
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span>{item.label}</span>
                        {item.badge && (
                          <Badge 
                            count={item.badge} 
                            style={{ 
                              backgroundColor: item.badge === 'Hot' ? '#ef4444' : '#10b981',
                              fontSize: '9px',
                              height: '16px',
                              lineHeight: '16px'
                            }}
                          />
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>

            <motion.div
              className="space-y-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <Link to="/contact" onClick={closeMenu}>
                <Button
                  type="primary"
                  block
                  size="large"
                  icon={<RocketOutlined />}
                  className="h-12 rounded-xl font-semibold text-base shadow-md"
                  style={{
                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                    border: 'none'
                  }}
                >
                  Get Started
                </Button>
              </Link>

              <a href={`tel:${BRAND.phone}`}>
                <Button
                  block
                  size="large"
                  icon={<PhoneOutlined />}
                  className="h-12 rounded-xl font-semibold text-base border-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                >
                  Call Now
                </Button>
              </a>
            </motion.div>
          </div>

          <motion.div
            className="p-5 bg-gray-50 border-t border-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="text-sm font-semibold text-gray-900">Quick Contact</div>
                <div className="text-xs text-gray-500">Get in touch with us</div>
              </div>
              <div className="flex gap-2">
                <a href={`tel:${BRAND.phone}`}>
                  <Button
                    type="text"
                    icon={<PhoneOutlined />}
                    className="w-9 h-9 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200"
                  />
                </a>
                <a href={`mailto:${BRAND.email}`}>
                  <Button
                    type="text"
                    icon={<MailOutlined />}
                    className="w-9 h-9 rounded-lg bg-green-100 text-green-600 hover:bg-green-200"
                  />
                </a>
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm text-gray-600">{BRAND.phone}</div>
              <div className="text-sm text-gray-600">{BRAND.email}</div>
            </div>
          </motion.div>
        </div>
      </Drawer>
    </motion.nav>
  );
};

export default Navbar;
