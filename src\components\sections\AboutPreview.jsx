import { Link } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import {
  ArrowRightIcon,
  TrophyIcon,
  UsersIcon,
  TargetIcon,
  RocketLaunchIcon,
  StarIcon,
  CheckCircleIcon,
  BoltIcon,
  HeartIcon,
  CrownIcon,
  ShieldCheckIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import CountUp from 'react-countup';
import { BRAND } from '../../utils/constants';

const AboutPreview = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  const features = [
    {
      icon: TrophyIcon,
      title: 'Proven Results',
      description: 'Track record of delivering measurable growth for our clients',
      gradient: 'from-yellow-500 to-orange-500',
      stat: '300%',
      statLabel: 'Avg Growth'
    },
    {
      icon: UsersIcon,
      title: 'Expert Team',
      description: 'Certified professionals with years of industry experience',
      gradient: 'from-green-500 to-emerald-500',
      stat: '50+',
      statLabel: 'Specialists'
    },
    {
      icon: TargetIcon,
      title: 'Strategic Approach',
      description: 'Data-driven strategies tailored to your business goals',
      gradient: 'from-red-500 to-pink-500',
      stat: '98%',
      statLabel: 'Success Rate'
    }
  ];

  const stats = [
    { title: 'Happy Clients', value: 500, suffix: '+', icon: <TeamOutlined />, color: '#0057ff' },
    { title: 'Projects Done', value: 1200, suffix: '+', icon: <RocketOutlined />, color: '#10b981' },
    { title: 'Success Rate', value: 98, suffix: '%', icon: <TrophyOutlined />, color: '#f59e0b' },
    { title: 'Team Members', value: 25, suffix: '+', icon: <HeartFilled />, color: '#ef4444' }
  ];

  const teamMembers = [
    { name: 'Sarah Johnson', role: 'CEO & Founder', avatar: '👩‍💼' },
    { name: 'Mike Chen', role: 'Lead Developer', avatar: '👨‍💻' },
    { name: 'Emily Davis', role: 'Marketing Director', avatar: '👩‍🎨' },
    { name: 'Alex Wilson', role: 'SEO Specialist', avatar: '👨‍🔬' }
  ];

  return (
    <section
      ref={ref}
      className="py-20 bg-gray-50 dark:bg-gray-800 relative overflow-hidden"
      id="about"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-green-400/10 to-cyan-400/10 rounded-full blur-3xl"
          animate={{
            y: [0, 20, 0],
            x: [0, -15, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        />

        {/* Floating particles */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -50, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 6 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      <div className="container-custom relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          <Row gutter={[64, 64]} align="middle">
            {/* Enhanced Content */}
            <Col xs={24} lg={12}>
              <motion.div variants={itemVariants}>
                {/* Badge */}
                <motion.div
                  className="inline-block mb-6"
                  whileHover={{ scale: 1.05 }}
                >
                  <Badge.Ribbon
                    text={
                      <span className="flex items-center gap-1">
                        <CrownOutlined className="text-yellow-300" />
                        <span className="font-semibold">Industry Leaders</span>
                      </span>
                    }
                    color="#0057ff"
                  >
                    <Card
                      className="glass border-0"
                      style={{
                        background: 'rgba(255, 255, 255, 0.9)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '12px',
                        padding: '8px 16px'
                      }}
                    >
                      <Space align="center">
                        <motion.div
                          animate={{
                            rotate: [0, 360]
                          }}
                          transition={{
                            duration: 20,
                            repeat: Infinity,
                            ease: "linear"
                          }}
                        >
                          <GlobalOutlined className="text-blue-500 text-lg" />
                        </motion.div>
                        <span className="font-semibold text-gray-700">About {BRAND.name}</span>
                      </Space>
                    </Card>
                  </Badge.Ribbon>
                </motion.div>

                <Title
                  level={2}
                  className="text-display mb-6"
                  style={{
                    background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  Empowering Brands Digitally
                </Title>

                <Paragraph
                  className="text-xl leading-relaxed text-gray-600 mb-6"
                  style={{ fontSize: '20px', lineHeight: 1.7 }}
                >
                  We are a{' '}
                  <motion.span
                    className="font-semibold text-blue-600"
                    whileHover={{ scale: 1.05 }}
                  >
                    passionate team of digital marketing experts
                  </motion.span>
                  {' '}dedicated to helping businesses thrive in the digital landscape. With over{' '}
                  <motion.span
                    className="font-semibold text-green-600"
                    whileHover={{ scale: 1.05 }}
                  >
                    5 years of experience
                  </motion.span>
                  , we've helped hundreds of companies achieve their online goals.
                </Paragraph>

                <Paragraph
                  className="text-lg leading-relaxed text-gray-600 mb-8"
                  style={{ fontSize: '18px', lineHeight: 1.6 }}
                >
                  Our mission is to{' '}
                  <motion.span
                    className="font-semibold text-purple-600"
                    whileHover={{ scale: 1.05 }}
                  >
                    empower brands digitally
                  </motion.span>
                  {' '}by providing innovative, results-driven marketing solutions that deliver real business value and sustainable growth.
                </Paragraph>
                {/* Enhanced Features */}
                <div className="space-y-6 mb-8">
                  {features.map((feature, index) => (
                    <motion.div
                      key={index}
                      variants={itemVariants}
                      whileHover={{ x: 8, scale: 1.02 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Card
                        className="border-0 hover-lift"
                        style={{
                          background: 'rgba(255, 255, 255, 0.8)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: '16px',
                          border: '1px solid rgba(255, 255, 255, 0.2)'
                        }}
                        bodyStyle={{ padding: '20px' }}
                      >
                        <div className="flex items-start gap-4">
                          <motion.div
                            className="flex-shrink-0"
                            whileHover={{
                              rotate: 360,
                              scale: 1.1
                            }}
                            transition={{ duration: 0.5 }}
                          >
                            <div
                              className="w-14 h-14 rounded-2xl flex items-center justify-center"
                              style={{
                                background: feature.bgColor,
                                color: feature.color,
                                border: `2px solid ${feature.color}20`
                              }}
                            >
                              <span className="text-xl">{feature.icon}</span>
                            </div>
                          </motion.div>

                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <Title level={5} className="m-0 text-gray-900">
                                {feature.title}
                              </Title>
                              <div className="text-right">
                                <div
                                  className="text-lg font-bold"
                                  style={{ color: feature.color }}
                                >
                                  {feature.stat}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {feature.statLabel}
                                </div>
                              </div>
                            </div>
                            <Paragraph className="text-gray-600 m-0 text-sm leading-relaxed">
                              {feature.description}
                            </Paragraph>
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                {/* Team Preview */}
                <motion.div
                  variants={itemVariants}
                  className="mb-8"
                >
                  <Card
                    className="border-0"
                    style={{
                      background: 'linear-gradient(135deg, #0057ff10 0%, #1890ff10 100%)',
                      borderRadius: '20px',
                      border: '1px solid rgba(0, 87, 255, 0.1)'
                    }}
                    bodyStyle={{ padding: '24px' }}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <Title level={5} className="m-0 flex items-center gap-2">
                        <TeamOutlined className="text-blue-500" />
                        Meet Our Team
                      </Title>
                      <Badge
                        count={`${teamMembers.length}+ Experts`}
                        style={{
                          backgroundColor: '#0057ff',
                          fontSize: '10px'
                        }}
                      />
                    </div>

                    <div className="flex items-center gap-3">
                      <Avatar.Group maxCount={4} size="large">
                        {teamMembers.map((member, index) => (
                          <motion.div
                            key={index}
                            whileHover={{ scale: 1.1, y: -4 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Avatar
                              size={48}
                              style={{
                                backgroundColor: '#0057ff',
                                fontSize: '20px',
                                border: '3px solid white',
                                boxShadow: '0 4px 12px rgba(0, 87, 255, 0.2)'
                              }}
                            >
                              {member.avatar}
                            </Avatar>
                          </motion.div>
                        ))}
                      </Avatar.Group>

                      <div className="ml-4">
                        <Text className="text-gray-600 text-sm">
                          Certified professionals ready to help your business grow
                        </Text>
                      </div>
                    </div>
                  </Card>
                </motion.div>

                {/* CTA Button */}
                <motion.div
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link to="/about">
                    <Button
                      type="primary"
                      size="large"
                      icon={<ArrowRightOutlined />}
                      className="btn-gradient hover-glow"
                      style={{
                        height: '56px',
                        padding: '0 32px',
                        fontSize: '18px',
                        fontWeight: 700,
                        borderRadius: '16px',
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        border: 'none',
                        boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
                      }}
                    >
                      Learn More About Us
                    </Button>
                  </Link>
                </motion.div>
              </motion.div>
            </Col>

            {/* Enhanced Visual Section */}
            <Col xs={24} lg={12}>
              <motion.div variants={itemVariants} className="relative">
                <Tilt
                  tiltMaxAngleX={5}
                  tiltMaxAngleY={5}
                  perspective={1000}
                  scale={1.02}
                  transitionSpeed={1000}
                >
                  <div className="relative">
                    {/* Main Image */}
                    <motion.div
                      className="relative overflow-hidden rounded-3xl"
                      style={{
                        boxShadow: '0 25px 60px rgba(0, 0, 0, 0.15)'
                      }}
                      whileHover={{
                        boxShadow: '0 35px 80px rgba(0, 87, 255, 0.2)'
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <img
                        src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Team collaboration"
                        className="w-full h-auto"
                        style={{
                          borderRadius: '24px',
                          filter: 'brightness(1.1) contrast(1.1)'
                        }}
                      />

                      {/* Overlay gradient */}
                      <div
                        className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 to-purple-500/10 rounded-3xl"
                        style={{ mixBlendMode: 'overlay' }}
                      />
                    </motion.div>

                    {/* Floating Stats Cards */}
                    <motion.div
                      className="absolute -top-6 -left-6 hidden lg:block"
                      initial={{ opacity: 0, y: 20, rotate: -5 }}
                      animate={{ opacity: 1, y: 0, rotate: 0 }}
                      transition={{ duration: 0.8, delay: 1 }}
                      whileHover={{
                        scale: 1.1,
                        rotate: 5,
                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
                      }}
                    >
                      <Card
                        className="glass border-0"
                        style={{
                          borderRadius: '16px',
                          background: 'rgba(255, 255, 255, 0.95)',
                          backdropFilter: 'blur(10px)',
                          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.1)',
                          border: '1px solid rgba(255, 255, 255, 0.2)'
                        }}
                        bodyStyle={{ padding: '16px' }}
                      >
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <TrophyOutlined className="text-yellow-500" />
                            <span className="text-2xl font-bold text-blue-600">
                              {isInView && <CountUp end={500} duration={2.5} />}+
                            </span>
                          </div>
                          <div className="text-gray-600 text-sm font-medium">
                            Projects Completed
                          </div>
                        </div>
                      </Card>
                    </motion.div>

                    <motion.div
                      className="absolute -bottom-6 -right-6 hidden lg:block"
                      initial={{ opacity: 0, y: 20, rotate: 5 }}
                      animate={{ opacity: 1, y: 0, rotate: 0 }}
                      transition={{ duration: 0.8, delay: 1.2 }}
                      whileHover={{
                        scale: 1.1,
                        rotate: -5,
                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
                      }}
                    >
                      <Card
                        className="glass border-0"
                        style={{
                          borderRadius: '16px',
                          background: 'rgba(255, 255, 255, 0.95)',
                          backdropFilter: 'blur(10px)',
                          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.1)',
                          border: '1px solid rgba(255, 255, 255, 0.2)'
                        }}
                        bodyStyle={{ padding: '16px' }}
                      >
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <StarFilled className="text-yellow-500" />
                            <span className="text-2xl font-bold text-green-600">
                              {isInView && <CountUp end={98} duration={2.5} />}%
                            </span>
                          </div>
                          <div className="text-gray-600 text-sm font-medium">
                            Success Rate
                          </div>
                        </div>
                      </Card>
                    </motion.div>

                    {/* Decorative Elements */}
                    <motion.div
                      className="absolute top-1/4 -right-8 w-16 h-16 rounded-full opacity-20 hidden xl:block"
                      style={{ background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)' }}
                      animate={{
                        y: [0, -20, 0],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />

                    <motion.div
                      className="absolute bottom-1/4 -left-8 w-12 h-12 rounded-full opacity-20 hidden xl:block"
                      style={{ background: 'linear-gradient(135deg, #10b981 0%, #34d399 100%)' }}
                      animate={{
                        y: [0, 15, 0],
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 1
                      }}
                    />
                  </div>
                </Tilt>

                {/* Stats Grid */}
                <motion.div
                  variants={itemVariants}
                  className="mt-8"
                >
                  <Row gutter={[16, 16]}>
                    {stats.map((stat, index) => (
                      <Col xs={12} sm={6} key={index}>
                        <motion.div
                          whileHover={{ scale: 1.05, y: -4 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Card
                            className="text-center border-0 hover-lift"
                            style={{
                              borderRadius: '16px',
                              background: 'rgba(255, 255, 255, 0.8)',
                              backdropFilter: 'blur(10px)',
                              border: '1px solid rgba(255, 255, 255, 0.2)'
                            }}
                            bodyStyle={{ padding: '16px' }}
                          >
                            <motion.div
                              className="inline-flex items-center justify-center w-10 h-10 rounded-xl mb-2"
                              style={{
                                background: `${stat.color}15`,
                                color: stat.color
                              }}
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.5 }}
                            >
                              {stat.icon}
                            </motion.div>

                            <div
                              className="text-lg font-bold mb-1"
                              style={{ color: stat.color }}
                            >
                              {isInView && (
                                <CountUp
                                  end={stat.value}
                                  duration={2.5}
                                  delay={index * 0.2}
                                />
                              )}
                              {stat.suffix}
                            </div>

                            <div className="text-gray-600 text-xs font-medium">
                              {stat.title}
                            </div>
                          </Card>
                        </motion.div>
                      </Col>
                    ))}
                  </Row>
                </motion.div>
              </motion.div>
            </Col>
          </Row>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutPreview;
