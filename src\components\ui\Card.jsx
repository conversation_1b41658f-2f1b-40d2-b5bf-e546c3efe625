import { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

const Card = forwardRef(({
  className,
  children,
  animate = true,
  variant = 'default',
  hover = true,
  ...props
}, ref) => {
  const variants = {
    default: 'bg-white dark:bg-gray-900 border-modern shadow-modern',
    glass: 'glass-card shadow-modern-lg',
    gradient: 'gradient-primary text-white shadow-modern-lg',
    outline: 'bg-transparent border-2 border-gray-300 dark:border-gray-600',
    elevated: 'bg-white dark:bg-gray-900 border-modern shadow-modern-lg',
  };

  const CardComponent = (
    <div
      ref={ref}
      className={cn(
        'rounded-2xl transition-all duration-300',
        variants[variant],
        hover && 'hover-lift',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={hover ? { y: -4 } : {}}
      >
        {CardComponent}
      </motion.div>
    );
  }

  return CardComponent;
});

Card.displayName = 'Card';

const CardHeader = forwardRef(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

const CardTitle = forwardRef(({ className, variant = 'default', ...props }, ref) => {
  const variants = {
    default: 'text-gray-900 dark:text-gray-100',
    gradient: 'text-gradient-primary',
    secondary: 'text-gradient-secondary',
    accent: 'text-gradient-accent',
  };

  return (
    <h3
      ref={ref}
      className={cn('text-2xl font-bold leading-tight tracking-tight', variants[variant], className)}
      {...props}
    />
  );
});
CardTitle.displayName = 'CardTitle';

const CardDescription = forwardRef(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-base text-gray-600 dark:text-gray-400 leading-relaxed', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

const CardContent = forwardRef(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
));
CardContent.displayName = 'CardContent';

const CardFooter = forwardRef(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-4 border-t border-gray-200 dark:border-gray-700', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
export default Card;
