import { useState } from 'react';
import { Form, Input, Select, Button, message, Card, Space } from 'antd';
import { SendOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { SERVICE_OPTIONS } from '../../utils/constants';

const { TextArea } = Input;
const { Option } = Select;

const ContactForm = () => {
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (values) => {
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setIsSubmitted(true);
      form.resetFields();
      message.success('Message sent successfully! We\'ll get back to you within 24 hours.');
    } catch (error) {
      console.error('Form submission error:', error);
      message.error('Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card
        style={{
          background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
          border: '1px solid #b7eb8f',
          borderRadius: '16px',
          textAlign: 'center'
        }}
        bodyStyle={{ padding: '48px 32px' }}
      >
        <div style={{
          width: '80px',
          height: '80px',
          background: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          margin: '0 auto 24px auto'
        }}>
          <CheckCircleOutlined style={{ fontSize: '40px', color: 'white' }} />
        </div>
        <h3 style={{ fontSize: '24px', fontWeight: 'bold', color: '#389e0d', marginBottom: '16px' }}>
          Message Sent Successfully!
        </h3>
        <p style={{ fontSize: '16px', color: '#52c41a', marginBottom: '24px' }}>
          Thank you for your interest. We'll get back to you within 24 hours.
        </p>
        <Button
          onClick={() => setIsSubmitted(false)}
          style={{
            borderColor: '#52c41a',
            color: '#52c41a'
          }}
        >
          Send Another Message
        </Button>
      </Card>
    );
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      size="large"
      style={{ width: '100%' }}
    >
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '24px' }}>
        <Form.Item
          label="Full Name"
          name="name"
          rules={[
            { required: true, message: 'Please enter your full name' },
            { min: 2, message: 'Name must be at least 2 characters' }
          ]}
        >
          <Input
            placeholder="Enter your full name"
            style={{ borderRadius: '8px', height: '48px' }}
          />
        </Form.Item>

        <Form.Item
          label="Email Address"
          name="email"
          rules={[
            { required: true, message: 'Please enter your email address' },
            { type: 'email', message: 'Please enter a valid email address' }
          ]}
        >
          <Input
            placeholder="Enter your email address"
            style={{ borderRadius: '8px', height: '48px' }}
          />
        </Form.Item>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '24px' }}>
        <Form.Item
          label="Phone Number"
          name="phone"
          rules={[
            { required: true, message: 'Please enter your phone number' },
            { pattern: /^[\+]?[\d\s\-\(\)]+$/, message: 'Please enter a valid phone number' }
          ]}
        >
          <Input
            placeholder="Enter your phone number"
            style={{ borderRadius: '8px', height: '48px' }}
          />
        </Form.Item>

        <Form.Item
          label="Service Interest"
          name="service"
          rules={[{ required: true, message: 'Please select a service' }]}
        >
          <Select
            placeholder="Select a service"
            style={{ borderRadius: '8px' }}
          >
            {SERVICE_OPTIONS.map((service) => (
              <Option key={service} value={service}>
                {service}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </div>

      <Form.Item
        label="Message"
        name="message"
        rules={[
          { required: true, message: 'Please enter your message' },
          { min: 10, message: 'Message must be at least 10 characters' }
        ]}
      >
        <TextArea
          rows={5}
          placeholder="Tell us about your project and goals..."
          style={{ borderRadius: '8px' }}
        />
      </Form.Item>

      <Form.Item style={{ marginBottom: 0 }}>
        <Button
          type="primary"
          htmlType="submit"
          loading={isSubmitting}
          icon={<SendOutlined />}
          style={{
            width: '100%',
            height: '56px',
            borderRadius: '12px',
            fontSize: '18px',
            fontWeight: 600,
            background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
            border: 'none',
            boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
          }}
        >
          {isSubmitting ? 'Sending Message...' : 'Send Message'}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ContactForm;
