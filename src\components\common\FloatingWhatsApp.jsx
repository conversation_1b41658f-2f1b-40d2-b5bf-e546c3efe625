import { useState, useEffect } from 'react';
import { WhatsAppOutlined, CloseOutlined, MessageOutlined } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { BRAND } from '../../utils/constants';

const FloatingWhatsApp = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      // Hide when scrolling up, show when scrolling down
      const currentScrollY = window.scrollY;
      setIsVisible(currentScrollY > 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleChat = () => setIsOpen(!isOpen);

  const openWhatsApp = () => {
    const phoneNumber = BRAND.phone?.replace(/[^0-9]/g, '');
    const message = encodeURIComponent("Hi! I'm interested in your services. Can you help me?");
    window.open(`https://wa.me/${phoneNumber}?text=${message}`, '_blank');
    setIsOpen(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <div className="fixed bottom-6 right-6 z-50">
          {/* Chat Popup */}
          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="absolute bottom-20 right-0 w-80 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden"
                style={{ 
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                  backdropFilter: 'blur(20px)'
                }}
              >
                {/* Header */}
                <div
                  className="bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 p-5 text-white relative overflow-hidden"
                >
                  <div className="flex items-center gap-4 relative z-10">
                    <div className="w-14 h-14 rounded-full bg-white/25 flex items-center justify-center backdrop-blur-sm border border-white/30">
                      <WhatsAppOutlined className="text-white text-2xl" />
                    </div>
                    <div>
                      <h3 className="font-bold text-xl">{BRAND.name}</h3>
                      <p className="text-green-100 text-sm flex items-center gap-1">
                        <span className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></span>
                        Typically replies instantly
                      </p>
                    </div>
                  </div>
                  
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-300/20 to-orange-300/20 rounded-full -translate-y-16 translate-x-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-300/20 to-purple-300/20 rounded-full translate-y-12 -translate-x-12"></div>
                  <div className="absolute top-1/2 left-1/2 w-16 h-16 bg-gradient-to-r from-pink-300/10 to-red-300/10 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <div className="flex items-start gap-3 mb-4">
                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                      <MessageOutlined className="text-green-600 text-sm" />
                    </div>
                    <div className="bg-gray-50 rounded-2xl rounded-tl-sm p-3 flex-1">
                      <p className="text-gray-800 text-sm leading-relaxed">
                        Hi there! 👋<br />
                        How can we help you today? We're here to assist with all your digital marketing needs!
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <motion.button
                      onClick={openWhatsApp}
                      whileHover={{ scale: 1.02, y: -1 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-5 rounded-2xl transition-all duration-300 flex items-center justify-center gap-3 shadow-xl hover:shadow-2xl"
                    >
                      <WhatsAppOutlined className="text-xl" />
                      💬 Start Chat on WhatsApp
                    </motion.button>
                    
                    <div className="text-center">
                      <span className="text-xs text-gray-500">
                        We'll respond as soon as possible
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Floating Button */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            exit={{ scale: 0, rotate: 180 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="relative"
          >
            <motion.button
              onClick={toggleChat}
              whileHover={{ scale: 1.15, rotate: 5 }}
              whileTap={{ scale: 0.9 }}
              className="w-18 h-18 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 hover:from-green-500 hover:via-emerald-600 hover:to-teal-600 rounded-full shadow-2xl flex items-center justify-center text-white transition-all duration-300 relative overflow-hidden group"
              style={{
                boxShadow: '0 12px 40px rgba(34, 197, 94, 0.5)',
                width: '72px',
                height: '72px'
              }}
            >
              {/* Background animation */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.3, 0.1, 0.3],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              
              {/* Icon */}
              <motion.div
                animate={{ rotate: isOpen ? 45 : 0 }}
                transition={{ duration: 0.3 }}
                className="relative z-10"
              >
                {isOpen ? (
                  <CloseOutlined className="text-2xl" />
                ) : (
                  <WhatsAppOutlined className="text-3xl" />
                )}
              </motion.div>

              {/* Pulse effect */}
              <motion.div
                className="absolute inset-0 rounded-full border-3 border-gradient-to-r from-green-400 to-emerald-400"
                style={{
                  border: '3px solid',
                  borderImage: 'linear-gradient(45deg, #10b981, #34d399) 1'
                }}
                animate={{
                  scale: [1, 1.8, 1],
                  opacity: [0.9, 0, 0.9],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: "easeOut"
                }}
              />
            </motion.button>

            {/* Notification badge */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1, duration: 0.3 }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white"
            >
              <motion.span
                className="text-white text-xs font-bold"
                animate={{
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity
                }}
              >
                1
              </motion.span>
            </motion.div>

            {/* Tooltip */}
            <AnimatePresence>
              {!isOpen && (
                <motion.div
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  transition={{ delay: 2, duration: 0.3 }}
                  className="absolute right-20 top-1/2 -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap"
                >
                  Need help? Chat with us!
                  <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-full">
                    <div className="w-0 h-0 border-l-4 border-l-gray-900 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default FloatingWhatsApp;
