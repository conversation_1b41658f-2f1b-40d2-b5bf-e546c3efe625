import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Layout, <PERSON>u, <PERSON><PERSON>, Drawer, Space } from 'antd';
import { MenuOutlined, PhoneOutlined } from '@ant-design/icons';
import { NAV_LINKS, BRAND } from '../../utils/constants';

const { Header } = Layout;

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  // Convert nav links to Ant Design menu items
  const menuItems = NAV_LINKS.map((link) => ({
    key: link.path,
    label: (
      <Link
        to={link.path}
        style={{
          color: location.pathname === link.path ? '#0057ff' : 'inherit',
          fontWeight: location.pathname === link.path ? 600 : 400
        }}
      >
        {link.name}
      </Link>
    ),
  }));

  return (
    <Header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'shadow-lg' : ''
      }`}
      style={{
        background: isScrolled ? '#fff' : 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        padding: '0 24px',
        height: '70px',
        lineHeight: '70px'
      }}
    >
      <div className="max-w-7xl mx-auto flex justify-between items-center h-full">
        {/* Logo */}
        <Link to="/" className="flex items-center space-x-3" onClick={closeMenu}>
          <div
            className="w-10 h-10 rounded-lg flex items-center justify-center"
            style={{ background: '#0057ff' }}
          >
            <span className="text-white font-bold text-xl">B</span>
          </div>
          <span className="text-2xl font-bold" style={{ color: '#000' }}>
            {BRAND.name}
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            style={{
              border: 'none',
              background: 'transparent',
              fontSize: '16px',
              fontWeight: 500
            }}
            className="flex-1"
          />
          <Space size="middle" style={{ marginLeft: '24px' }}>
            <Link to="/contact">
              <Button
                type="primary"
                size="large"
                icon={<PhoneOutlined />}
                style={{
                  borderRadius: '8px',
                  height: '44px',
                  fontWeight: 600,
                  boxShadow: '0 4px 12px rgba(0, 87, 255, 0.3)'
                }}
              >
                Get Free Audit
              </Button>
            </Link>
          </Space>
        </div>

        {/* Mobile menu button */}
        <div className="lg:hidden">
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={toggleMenu}
            size="large"
            style={{ color: '#0057ff' }}
          />
        </div>
      </div>

      {/* Mobile Navigation Drawer */}
      <Drawer
        title={
          <div className="flex items-center space-x-3">
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center"
              style={{ background: '#0057ff' }}
            >
              <span className="text-white font-bold text-lg">B</span>
            </div>
            <span className="text-xl font-bold">{BRAND.name}</span>
          </div>
        }
        placement="right"
        onClose={closeMenu}
        open={isOpen}
        width={300}
        styles={{
          body: { padding: 0 }
        }}
      >
        <Menu
          mode="vertical"
          selectedKeys={[location.pathname]}
          items={[
            ...menuItems,
            {
              key: 'cta',
              label: (
                <Link to="/contact" onClick={closeMenu}>
                  <Button
                    type="primary"
                    block
                    size="large"
                    icon={<PhoneOutlined />}
                    style={{
                      marginTop: '16px',
                      borderRadius: '8px',
                      height: '44px',
                      fontWeight: 600
                    }}
                  >
                    Get Free Audit
                  </Button>
                </Link>
              ),
            }
          ]}
          style={{
            border: 'none',
            fontSize: '16px'
          }}
        />
      </Drawer>
    </Header>
  );
};

export default Navbar;
