import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { useEffect } from 'react';
import AOS from 'aos';
import Layout from './components/layout/Layout';
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import CaseStudies from './pages/CaseStudies';
import Blog from './pages/Blog';
import Contact from './pages/Contact';


function App() {
  // Initialize AOS (Animate On Scroll)
  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: 'ease-out-cubic',
      once: true,
      offset: 100,
      delay: 100,
    });
  }, []);

  const theme = {
    token: {
      // Primary Colors
      colorPrimary: '#0057ff',
      colorPrimaryBg: '#eff6ff',
      colorPrimaryBgHover: '#dbeafe',
      colorPrimaryBorder: '#bfdbfe',
      colorPrimaryBorderHover: '#93c5fd',
      colorPrimaryHover: '#1890ff',
      colorPrimaryActive: '#0041cc',
      colorPrimaryTextHover: '#1890ff',
      colorPrimaryText: '#0057ff',
      colorPrimaryTextActive: '#0041cc',

      // Success Colors
      colorSuccess: '#10b981',
      colorSuccessBg: '#ecfdf5',
      colorSuccessBorder: '#a7f3d0',

      // Warning Colors
      colorWarning: '#f59e0b',
      colorWarningBg: '#fffbeb',
      colorWarningBorder: '#fed7aa',

      // Error Colors
      colorError: '#ef4444',
      colorErrorBg: '#fef2f2',
      colorErrorBorder: '#fecaca',

      // Info Colors
      colorInfo: '#3b82f6',
      colorInfoBg: '#eff6ff',
      colorInfoBorder: '#bfdbfe',

      // Typography
      fontFamily: 'Inter, Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 16,
      fontSizeHeading1: 48,
      fontSizeHeading2: 36,
      fontSizeHeading3: 28,
      fontSizeHeading4: 24,
      fontSizeHeading5: 20,
      fontSizeLG: 18,
      fontSizeSM: 14,
      fontSizeXL: 20,

      // Border Radius
      borderRadius: 12,
      borderRadiusLG: 16,
      borderRadiusSM: 8,
      borderRadiusXS: 4,

      // Spacing
      padding: 16,
      paddingLG: 24,
      paddingSM: 12,
      paddingXS: 8,
      paddingXXS: 4,
      margin: 16,
      marginLG: 24,
      marginSM: 12,
      marginXS: 8,
      marginXXS: 4,

      // Layout
      controlHeight: 44,
      controlHeightLG: 52,
      controlHeightSM: 36,

      // Shadows
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      boxShadowSecondary: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',

      // Motion
      motionDurationFast: '0.15s',
      motionDurationMid: '0.3s',
      motionDurationSlow: '0.5s',
      motionEaseInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      motionEaseOut: 'cubic-bezier(0, 0, 0.2, 1)',
      motionEaseIn: 'cubic-bezier(0.4, 0, 1, 1)',

      // Line Height
      lineHeight: 1.6,
      lineHeightHeading1: 1.1,
      lineHeightHeading2: 1.2,
      lineHeightHeading3: 1.3,
      lineHeightHeading4: 1.4,
      lineHeightHeading5: 1.5,

      // Z-Index
      zIndexBase: 0,
      zIndexAffix: 10,
      zIndexBackTop: 10,
      zIndexModal: 1000,
      zIndexDrawer: 1000,
      zIndexPopover: 1030,
      zIndexDropdown: 1050,
      zIndexTooltip: 1070,

      // Wireframe
      wireframe: false,
    },
    components: {
      Button: {
        borderRadius: 12,
        controlHeight: 44,
        controlHeightLG: 52,
        controlHeightSM: 36,
        fontWeight: 600,
        primaryShadow: '0 8px 24px rgba(0, 87, 255, 0.3)',
        defaultShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        paddingInline: 24,
        paddingInlineLG: 32,
        paddingInlineSM: 16,
      },
      Card: {
        borderRadius: 16,
        borderRadiusLG: 20,
        paddingLG: 32,
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        boxShadowHover: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      },
      Input: {
        borderRadius: 12,
        controlHeight: 44,
        controlHeightLG: 52,
        controlHeightSM: 36,
        paddingInline: 16,
        fontSize: 16,
      },
      Select: {
        borderRadius: 12,
        controlHeight: 44,
        controlHeightLG: 52,
        controlHeightSM: 36,
      },
      Menu: {
        borderRadius: 12,
        itemBorderRadius: 8,
        itemHeight: 44,
        fontSize: 16,
        fontWeight: 500,
      },
      Typography: {
        titleMarginBottom: 16,
        titleMarginTop: 0,
      },
      Carousel: {
        dotHeight: 8,
        dotWidth: 24,
        dotActiveWidth: 32,
      },
      Modal: {
        borderRadius: 20,
        paddingLG: 32,
      },
      Drawer: {
        borderRadius: 20,
        paddingLG: 32,
      },
      Tooltip: {
        borderRadius: 8,
        fontSize: 14,
      },
      Popover: {
        borderRadius: 12,
        paddingLG: 20,
      },
      Form: {
        itemMarginBottom: 24,
        verticalLabelPadding: 8,
      },
      Table: {
        borderRadius: 12,
        headerBg: '#f8fafc',
        headerColor: '#374151',
        headerSortActiveBg: '#f1f5f9',
        headerSortHoverBg: '#f8fafc',
        rowHoverBg: '#f8fafc',
      },
      Tabs: {
        borderRadius: 12,
        cardBg: '#f8fafc',
        itemActiveColor: '#0057ff',
        itemHoverColor: '#1890ff',
        itemSelectedColor: '#0057ff',
        inkBarColor: '#0057ff',
        titleFontSize: 16,
        titleFontSizeLG: 18,
        titleFontSizeSM: 14,
      },
      Badge: {
        borderRadius: 12,
        fontSize: 12,
        fontWeight: 600,
        textFontSize: 12,
        textFontSizeSM: 10,
      },
      Tag: {
        borderRadius: 8,
        fontSize: 14,
        fontWeight: 500,
        paddingInline: 12,
      },
      Progress: {
        borderRadius: 8,
        lineCap: 'round',
      },
      Switch: {
        borderRadius: 16,
      },
      Slider: {
        borderRadius: 8,
        handleSize: 20,
        handleSizeHover: 24,
        railSize: 6,
        trackSize: 6,
      },
      Rate: {
        starSize: 24,
        starSizeSM: 20,
      },
      Upload: {
        borderRadius: 12,
      },
      Avatar: {
        borderRadius: 12,
        containerSize: 44,
        containerSizeLG: 52,
        containerSizeSM: 36,
      },
      Divider: {
        marginLG: 32,
        margin: 24,
        marginSM: 16,
      },
      Steps: {
        borderRadius: 8,
        dotSize: 32,
        iconSize: 16,
        titleLineHeight: 1.5,
        descriptionMaxWidth: 200,
      },
      Breadcrumb: {
        fontSize: 14,
        iconFontSize: 14,
        itemColor: '#6b7280',
        lastItemColor: '#374151',
        linkColor: '#0057ff',
        linkHoverColor: '#1890ff',
        separatorColor: '#9ca3af',
        separatorMargin: 8,
      },
      Pagination: {
        borderRadius: 8,
        itemSize: 36,
        itemSizeSM: 28,
        fontSize: 14,
        fontWeightStrong: 600,
      },
      Anchor: {
        linkPaddingBlock: 8,
        linkPaddingInlineStart: 16,
      },
      BackTop: {
        borderRadius: 12,
        zIndexPopup: 10,
      },
      Spin: {
        contentHeight: 400,
      },
      Empty: {
        fontSize: 14,
        imageHeight: 100,
      },
      Result: {
        titleFontSize: 24,
        subtitleFontSize: 16,
        iconFontSize: 72,
        extraMargin: 32,
      },
    },
    algorithm: [],
  };

  return (
    <ConfigProvider theme={theme}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Home />} />

            <Route path="/about" element={<About />} />
            <Route path="/services" element={<Services />} />
            <Route path="/case-studies" element={<CaseStudies />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/contact" element={<Contact />} />
          </Routes>
        </Layout>
      </Router>
    </ConfigProvider>
  );
}

export default App;
