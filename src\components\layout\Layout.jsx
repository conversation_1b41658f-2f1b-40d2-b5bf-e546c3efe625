import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Layout as AntLayout } from 'antd';
import Navbar from './Navbar';
import Footer from './Footer';
import WhatsAppButton from '../ui/WhatsAppButton';

const { Content } = AntLayout;

const Layout = ({ children }) => {
  const location = useLocation();

  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Navbar />
      <Content style={{ marginTop: '70px' }}>
        {children}
      </Content>
      <Footer />
      <WhatsAppButton />
    </AntLayout>
  );
};

export default Layout;
