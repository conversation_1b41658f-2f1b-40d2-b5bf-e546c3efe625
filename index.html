<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    <title>Brandify - Empowering Brands Digitally | Digital Marketing Agency</title>
    <meta name="description" content="Brandify is a leading digital marketing agency helping businesses grow online through SEO, social media marketing, PPC advertising, and web development services." />
    <meta name="keywords" content="digital marketing, SEO, social media marketing, PPC advertising, web development, branding, content marketing" />
    <meta name="author" content="Brandify" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Brandify - Empowering Brands Digitally" />
    <meta property="og:description" content="Leading digital marketing agency helping businesses grow online through proven strategies and innovative solutions." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://brandify.com" />
    <meta property="og:image" content="https://brandify.com/og-image.jpg" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Brandify - Empowering Brands Digitally" />
    <meta name="twitter:description" content="Leading digital marketing agency helping businesses grow online through proven strategies and innovative solutions." />
    <meta name="twitter:image" content="https://brandify.com/twitter-image.jpg" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://brandify.com" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
