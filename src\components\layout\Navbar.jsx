import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Layout, Button, Drawer, Space, Badge, Avatar, Dropdown } from 'antd';
import {
  MenuOutlined,
  PhoneOutlined,
  RocketOutlined,
  StarFilled,
  DownOutlined,
  GlobalOutlined,
  MailOutlined,
  CloseOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  ToolOutlined,
  ProjectOutlined,
  FileTextOutlined,
  ContactsOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_LINKS, BRAND } from '../../utils/constants';

const { Header } = Layout;

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const location = useLocation();

  // Navigation icons mapping
  const navIcons = {
    '/': <HomeOutlined />,
    '/about': <InfoCircleOutlined />,
    '/services': <ToolOutlined />,
    '/case-studies': <ProjectOutlined />,
    '/blog': <FileTextOutlined />,
    '/contact': <ContactsOutlined />
  };

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Show/hide navbar based on scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }

      // Add glassmorphism effect when scrolled
      setIsScrolled(currentScrollY > 10);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  // Services dropdown items
  const servicesDropdown = {
    items: [
      {
        key: 'seo',
        label: (
          <Link to="/services" className="flex items-center gap-3 p-2">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <GlobalOutlined className="text-blue-600" />
            </div>
            <div>
              <div className="font-semibold text-gray-800">SEO & SEM</div>
              <div className="text-xs text-gray-500">Search optimization</div>
            </div>
          </Link>
        ),
      },
      {
        key: 'social',
        label: (
          <Link to="/services" className="flex items-center gap-3 p-2">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <StarFilled className="text-purple-600" />
            </div>
            <div>
              <div className="font-semibold text-gray-800">Social Media</div>
              <div className="text-xs text-gray-500">Brand engagement</div>
            </div>
          </Link>
        ),
      },
      {
        key: 'ppc',
        label: (
          <Link to="/services" className="flex items-center gap-3 p-2">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <RocketOutlined className="text-green-600" />
            </div>
            <div>
              <div className="font-semibold text-gray-800">PPC Advertising</div>
              <div className="text-xs text-gray-500">Paid campaigns</div>
            </div>
          </Link>
        ),
      },
    ],
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          className="fixed top-0 left-0 right-0 z-50"
        >
          <Header
            className={`transition-all duration-500 ease-out ${
              isScrolled ? 'shadow-2xl' : 'shadow-md'
            }`}
            style={{
              background: isScrolled
                ? 'rgba(255, 255, 255, 0.95)'
                : 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              border: 'none',
              borderBottom: isScrolled ? '1px solid rgba(0, 87, 255, 0.08)' : '1px solid rgba(0, 0, 0, 0.05)',
              padding: '0',
              height: '85px',
              position: 'relative',
              overflow: 'hidden',
              boxShadow: isScrolled
                ? '0 10px 40px rgba(0, 87, 255, 0.08), 0 4px 12px rgba(0, 0, 0, 0.03)'
                : '0 2px 20px rgba(0, 0, 0, 0.04)'
            }}
          >
            {/* Subtle gradient overlay */}
            <div
              className="absolute inset-0 opacity-2 pointer-events-none"
              style={{
                background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
              }}
            />
            <div className="max-w-7xl mx-auto px-6 h-full flex items-center justify-between relative z-10">
              {/* Modern Logo Section */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
                className="flex items-center"
              >
                <Link to="/" className="flex items-center gap-4" onClick={closeMenu}>
                  {/* Logo */}
                  <div className="relative">
                    <motion.div
                      className="w-14 h-14 rounded-2xl flex items-center justify-center relative overflow-hidden"
                      style={{
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        boxShadow: '0 8px 32px rgba(0, 87, 255, 0.25)'
                      }}
                      whileHover={{
                        boxShadow: '0 12px 40px rgba(0, 87, 255, 0.35)',
                        y: -2
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <span className="text-white font-bold text-2xl relative z-10">B</span>

                      {/* Shine effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0"
                        whileHover={{ opacity: 0.2, x: ['0%', '100%'] }}
                        transition={{ duration: 0.6 }}
                      />
                    </motion.div>

                    {/* Status indicator */}
                    <motion.div
                      className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"
                      animate={{
                        scale: [1, 1.2, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                      }}
                    />
                  </div>

                  {/* Brand Text */}
                  <div className="flex flex-col">
                    <span
                      className="text-2xl font-bold"
                      style={{
                        background: 'linear-gradient(135deg, #1a202c 0%, #0057ff 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text'
                      }}
                    >
                      {BRAND.name}
                    </span>
                    <span className="text-xs text-gray-500 font-medium -mt-1">
                      Digital Excellence
                    </span>
                  </div>
                </Link>
              </motion.div>

              {/* Modern Desktop Navigation */}
              <div className="hidden lg:flex items-center gap-8">
                {/* Navigation Links */}
                <nav className="flex items-center gap-2">
                  {NAV_LINKS.map((link) => (
                    <motion.div
                      key={link.path}
                      whileHover={{ y: -2 }}
                      transition={{ duration: 0.2 }}
                    >
                      {link.name === 'Services' ? (
                        <Dropdown menu={servicesDropdown} trigger={['hover']} placement="bottomLeft">
                          <Link
                            to={link.path}
                            className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                              location.pathname === link.path
                                ? 'bg-blue-50 text-blue-600 shadow-sm'
                                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                            }`}
                          >
                            {navIcons[link.path]}
                            <span>{link.name}</span>
                            <DownOutlined className="text-xs" />
                          </Link>
                        </Dropdown>
                      ) : (
                        <Link
                          to={link.path}
                          className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                            location.pathname === link.path
                              ? 'bg-blue-50 text-blue-600 shadow-sm'
                              : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                          }`}
                        >
                          {navIcons[link.path]}
                          <span>{link.name}</span>
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </nav>

                {/* Action Buttons */}
                <div className="flex items-center gap-3">
                  {/* Contact Button */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      type="text"
                      icon={<PhoneOutlined />}
                      className="flex items-center gap-2 px-4 py-2 h-auto rounded-xl font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300"
                    >
                      <span className="hidden xl:inline">Call</span>
                    </Button>
                  </motion.div>

                  {/* CTA Button */}
                  <motion.div
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link to="/contact">
                      <Button
                        type="primary"
                        icon={<RocketOutlined />}
                        className="h-12 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                        style={{
                          background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                          border: 'none',
                          fontSize: '15px'
                        }}
                      >
                        Get Started
                      </Button>
                    </Link>
                  </motion.div>
                </div>
              </div>

              {/* Modern Mobile Menu Button */}
              <div className="lg:hidden">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    type="text"
                    onClick={toggleMenu}
                    className="w-12 h-12 rounded-xl flex items-center justify-center hover:bg-blue-50 transition-all duration-300"
                    style={{
                      color: isOpen ? '#0057ff' : '#374151',
                      background: isOpen ? 'rgba(0, 87, 255, 0.1)' : 'transparent'
                    }}
                  >
                    <motion.div
                      animate={{ rotate: isOpen ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {isOpen ? <CloseOutlined className="text-lg" /> : <MenuOutlined className="text-lg" />}
                    </motion.div>
                  </Button>
                </motion.div>
              </div>
            </div>

            {/* Modern Mobile Navigation Drawer */}
            <Drawer
              title={
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-2xl flex items-center justify-center"
                    style={{
                      background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                      boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
                    }}
                  >
                    <span className="text-white font-bold text-xl">B</span>
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900">{BRAND.name}</div>
                    <div className="text-sm text-gray-500">Digital Excellence</div>
                  </div>
                </div>
              }
              placement="right"
              onClose={closeMenu}
              open={isOpen}
              width={360}
              styles={{
                body: {
                  padding: 0,
                  background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
                },
                header: {
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(20px)',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
                  padding: '20px 24px'
                }
              }}
              closeIcon={<CloseOutlined className="text-gray-500 hover:text-gray-700" />}
            >
              <div className="flex flex-col h-full">
                {/* Navigation Links */}
                <div className="flex-1 p-6">
                  <div className="space-y-1 mb-8">
                    {NAV_LINKS.map((link, index) => (
                      <motion.div
                        key={link.path}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Link
                          to={link.path}
                          onClick={closeMenu}
                          className={`flex items-center gap-4 px-4 py-4 rounded-2xl text-lg font-medium transition-all duration-300 ${
                            location.pathname === link.path
                              ? 'bg-blue-50 text-blue-600 shadow-sm border border-blue-100'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                          }`}
                        >
                          <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                            location.pathname === link.path
                              ? 'bg-blue-100 text-blue-600'
                              : 'bg-gray-100 text-gray-500'
                          }`}>
                            {navIcons[link.path]}
                          </div>
                          <span>{link.name}</span>
                        </Link>
                      </motion.div>
                    ))}
                  </div>

                  {/* Quick Actions */}
                  <motion.div
                    className="space-y-3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Link to="/contact" onClick={closeMenu}>
                      <Button
                        type="primary"
                        block
                        size="large"
                        icon={<RocketOutlined />}
                        className="h-14 rounded-2xl font-semibold text-lg shadow-lg"
                        style={{
                          background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                          border: 'none'
                        }}
                      >
                        Get Started
                      </Button>
                    </Link>

                    <Button
                      block
                      size="large"
                      icon={<PhoneOutlined />}
                      className="h-14 rounded-2xl font-semibold text-lg border-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                    >
                      Call Now
                    </Button>
                  </motion.div>
                </div>

                {/* Footer Section */}
                <motion.div
                  className="p-6 bg-gray-50 border-t border-gray-100"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <div className="text-sm font-semibold text-gray-900">Quick Contact</div>
                      <div className="text-xs text-gray-500">Get in touch with us</div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="text"
                        icon={<PhoneOutlined />}
                        className="w-10 h-10 rounded-xl bg-blue-100 text-blue-600 hover:bg-blue-200"
                      />
                      <Button
                        type="text"
                        icon={<MailOutlined />}
                        className="w-10 h-10 rounded-xl bg-green-100 text-green-600 hover:bg-green-200"
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <div className="text-sm text-gray-600">{BRAND.phone}</div>
                    <div className="text-sm text-gray-600">{BRAND.email}</div>
                  </div>
                </motion.div>
              </div>
            </Drawer>
          </Header>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Navbar;
