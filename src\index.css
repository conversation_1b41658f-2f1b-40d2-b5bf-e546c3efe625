@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import 'aos/dist/aos.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Full Width Navbar Styles */
.navbar-full-width {
  width: 100vw !important;
  margin-left: calc(-50vw + 50%) !important;
  margin-right: calc(-50vw + 50%) !important;
}

/* Seamless Section Connection */
.seamless-section {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Remove any default margins/padding that might create gaps */
body {
  margin: 0;
  padding: 0;
}

#root {
  margin: 0;
  padding: 0;
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom container for full-width sections */
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

@media (min-width: 1280px) {
  .container-custom {
    padding: 0 48px;
  }
}

/* Full-width navbar specific styles */
.navbar-full-width {
  width: 100vw;
  left: 0;
  right: 0;
  margin: 0;
  padding: 0;
}

/* Ensure navbar content spans full width */
.navbar-content {
  width: 100%;
  max-width: none;
  margin: 0;
}

/* Remove any default container constraints */
.ant-layout {
  background: transparent;
}

/* Ensure smooth transitions */
* {
  box-sizing: border-box;
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
  .navbar-full-width {
    width: 100vw;
  }
}

/* Navbar alignment fixes */
.navbar-logo-section {
  min-width: 320px;
  display: flex;
  align-items: center;
}

.navbar-nav-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 2rem;
}

.navbar-actions-section {
  min-width: 320px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Icon alignment fixes */
.ant-btn .anticon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Badge positioning */
.ant-badge {
  display: inline-flex;
  align-items: center;
}

/* Button spacing fixes */
.ant-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Navigation item spacing */
.nav-item {
  margin: 0 0.25rem;
}

/* Ensure proper vertical alignment */
.navbar-container {
  display: flex;
  align-items: center;
  height: 100%;
}

/* Fix icon alignment in navigation */
.ant-btn .anticon {
  vertical-align: middle;
  line-height: 1;
}

/* Improve badge positioning */
.ant-badge-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Navigation link improvements */
.nav-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
  .navbar-mobile {
    padding: 0 1rem;
  }

  .navbar-mobile .ant-btn {
    min-width: auto;
  }
}

/* WhatsApp icon enhancements */
.whatsapp-icon {
  position: relative;
  z-index: 10;
}

.whatsapp-icon:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4) !important;
}

.whatsapp-icon::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #25d366, #128c7e);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.whatsapp-icon:hover::before {
  opacity: 0.2;
}

/* Pulse animation for WhatsApp icon */
@keyframes whatsapp-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

.whatsapp-pulse {
  animation: whatsapp-pulse 2s infinite;
}

/* Floating WhatsApp button specific styles */
.floating-whatsapp {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.floating-whatsapp-button {
  background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
  box-shadow: 0 8px 30px rgba(37, 211, 102, 0.4);
  transition: all 0.3s ease;
}

.floating-whatsapp-button:hover {
  background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
  box-shadow: 0 12px 40px rgba(37, 211, 102, 0.6);
  transform: translateY(-2px);
}

/* Floating WhatsApp chat popup */
.whatsapp-chat-popup {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Mobile responsive adjustments for floating button */
@media (max-width: 768px) {
  .floating-whatsapp {
    bottom: 20px;
    right: 20px;
  }

  .whatsapp-chat-popup {
    width: calc(100vw - 40px) !important;
    max-width: 320px;
  }
}

/* Enhanced navbar styling */
.navbar-enhanced {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.navbar-enhanced.scrolled {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.15);
}

/* Navigation item hover effects */
.nav-item-enhanced {
  position: relative;
  overflow: hidden;
}

.nav-item-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-item-enhanced:hover::before {
  left: 100%;
}

/* Colorful gradient text */
.gradient-text {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button animations */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Floating elements animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating-element {
  animation: float 3s ease-in-out infinite;
}

/* Colorful shadows */
.shadow-colorful {
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2), 0 5px 15px rgba(139, 92, 246, 0.1);
}

.shadow-colorful:hover {
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.3), 0 8px 20px rgba(139, 92, 246, 0.2);
}

/* Gradient animation for hero text */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced hero section styling */
.text-hero {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-weight: 900;
  letter-spacing: -0.025em;
}

/* Improved glassmorphism effects */
.glass-enhanced {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Colorful hover effects for hero elements */
.hero-element:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Enhanced button animations */
.btn-hero {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

.btn-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.btn-hero:hover::before {
  left: 100%;
}

/* CSS Variables for Design System */
:root {
  /* Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #0057ff;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #0057ff 0%, #1890ff 100%);
  --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-dark: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-primary: 0 8px 24px rgba(0, 87, 255, 0.3);
  --shadow-primary-lg: 0 20px 40px rgba(0, 87, 255, 0.4);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;

  /* Spacing */
  --space-xs: 0.5rem;
  --space-sm: 1rem;
  --space-md: 1.5rem;
  --space-lg: 2rem;
  --space-xl: 3rem;
  --space-2xl: 4rem;
  --space-3xl: 6rem;

  /* Typography */
  --font-primary: 'Inter', 'Poppins', sans-serif;
  --font-secondary: 'Poppins', sans-serif;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: #1a202c;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
  transition: var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0041cc 0%, #1570ef 100%);
}

/* Global Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility Classes */
.animate-pulse {
  animation: pulse 4s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

/* Glassmorphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hover Effects */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.hover-scale {
  transition: var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-primary-lg);
}

/* Button Enhancements */
.btn-gradient {
  background: var(--gradient-primary);
  border: none;
  color: white;
  transition: var(--transition-normal);
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #0041cc 0%, #1570ef 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary-lg);
}

/* Card Enhancements */
.card-modern {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  transition: var(--transition-normal);
}

.card-modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  background: rgba(255, 255, 255, 0.9);
}

/* Section Spacing */
.section-padding {
  padding: var(--space-3xl) 0;
}

.section-padding-sm {
  padding: var(--space-2xl) 0;
}

/* Container */
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Responsive Typography */
.text-hero {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
}

.text-display {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  line-height: 1.2;
}

.text-heading {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
  line-height: 1.3;
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 87, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 87, 255, 0.8), 0 0 30px rgba(0, 87, 255, 0.6);
  }
}

/* Enhanced Navbar Styles */
.nav-menu .ant-menu-item {
  border-radius: 8px !important;
  margin: 0 4px !important;
  transition: all 0.3s ease !important;
}

.nav-menu .ant-menu-item:hover {
  background: rgba(0, 87, 255, 0.1) !important;
  transform: translateY(-2px) !important;
}

.nav-menu .ant-menu-item-selected {
  background: linear-gradient(135deg, rgba(0, 87, 255, 0.15) 0%, rgba(24, 144, 255, 0.15) 100%) !important;
  box-shadow: 0 4px 12px rgba(0, 87, 255, 0.2) !important;
}

.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #0057ff 0%, #1890ff 100%);
  transition: width 0.3s ease;
}

.nav-link:hover::before {
  width: 100%;
}

.active-nav-link::before {
  width: 100%;
}

/* Mobile Drawer Enhancements */
.mobile-drawer .ant-drawer-content {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
}

.mobile-drawer .ant-drawer-header {
  border-bottom: 1px solid rgba(0, 87, 255, 0.1) !important;
}

/* Footer Enhancements */
.footer-gradient {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #1a202c 100%);
  position: relative;
  overflow: hidden;
}

.footer-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 87, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(24, 144, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(64, 169, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.footer-section {
  position: relative;
  z-index: 10;
}

.footer-link {
  position: relative;
  transition: all 0.3s ease;
}

.footer-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(135deg, #0057ff 0%, #1890ff 100%);
  transition: width 0.3s ease;
}

.footer-link:hover::before {
  width: 100%;
}

.footer-link:hover {
  color: #60a5fa !important;
  transform: translateX(4px);
}

.social-icon {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #0057ff 0%, #1890ff 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.social-icon:hover::before {
  opacity: 0.1;
}

.social-icon:hover {
  transform: translateY(-4px) scale(1.1);
  box-shadow: 0 8px 24px rgba(0, 87, 255, 0.3);
}

/* Animated Background Patterns */
.pattern-dots {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: float 20s ease-in-out infinite;
}

.pattern-grid {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
}

/* Enhanced Button Styles */
.btn-modern {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0057ff 0%, #1890ff 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 87, 255, 0.3);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 87, 255, 0.4);
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.card-hover::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0, 87, 255, 0.05) 0%, rgba(24, 144, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-hover:hover::before {
  opacity: 1;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Text Animations */
.text-shimmer {
  background: linear-gradient(90deg, #374151 25%, #60a5fa 50%, #374151 75%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
}

/* Carousel Enhancements */
.testimonials-carousel .ant-carousel .slick-slide {
  padding: 0 8px;
}

.testimonials-carousel .ant-carousel .slick-track {
  display: flex !important;
  align-items: center;
}

.testimonials-carousel .ant-carousel .slick-slide > div {
  height: 100%;
}

.testimonials-carousel .ant-carousel .slick-slide > div > div {
  height: 100%;
  display: flex;
  align-items: center;
}

/* Loading Animations */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Micro-interactions */
.micro-bounce {
  transition: transform 0.2s ease;
}

.micro-bounce:hover {
  transform: translateY(-2px);
}

.micro-scale {
  transition: transform 0.2s ease;
}

.micro-scale:hover {
  transform: scale(1.05);
}

.micro-rotate {
  transition: transform 0.3s ease;
}

.micro-rotate:hover {
  transform: rotate(5deg);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #0057ff 0%, #1890ff 100%);
  border-radius: 6px;
  border: 2px solid #f1f5f9;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0041cc 0%, #1570ef 100%);
  border: 2px solid #e2e8f0;
}

/* Selection Styling */
::selection {
  background: rgba(0, 87, 255, 0.2);
  color: #0057ff;
}

::-moz-selection {
  background: rgba(0, 87, 255, 0.2);
  color: #0057ff;
}

/* Focus Styles */
.ant-btn:focus,
.ant-input:focus,
.ant-select-selector:focus {
  box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.2) !important;
  border-color: #0057ff !important;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .nav-menu .ant-menu-item {
    margin: 2px 0 !important;
  }

  .footer-gradient {
    padding: 2rem 0;
  }

  .card-hover:hover {
    transform: translateY(-4px);
  }

  .testimonials-carousel .ant-carousel .slick-slide {
    padding: 0 4px;
  }

  .text-hero {
    font-size: clamp(2rem, 8vw, 3rem) !important;
  }

  .text-display {
    font-size: clamp(1.5rem, 6vw, 2.5rem) !important;
  }

  .container-custom {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .section-padding {
    padding: 3rem 0;
  }

  .section-padding-sm {
    padding: 2rem 0;
  }
}
