import { Link } from 'react-router-dom';
import { Row, Col, Button, Typography, Space, Statistic, Card } from 'antd';
import { ArrowRightOutlined, PlayCircleOutlined, TrophyOutlined, TeamOutlined, CalendarOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { BRAND } from '../../utils/constants';

const { Title, Paragraph } = Typography;

const Hero = () => {
  return (
    <section
      style={{
        background: 'linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%)',
        padding: '120px 0 80px 0',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Pattern */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.05,
          pointerEvents: 'none'
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: '10%',
            left: '10%',
            width: '300px',
            height: '300px',
            background: '#0057ff',
            borderRadius: '50%',
            filter: 'blur(100px)',
            animation: 'pulse 4s ease-in-out infinite'
          }}
        />
        <div
          style={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: '200px',
            height: '200px',
            background: '#40a9ff',
            borderRadius: '50%',
            filter: 'blur(80px)',
            animation: 'pulse 4s ease-in-out infinite 2s'
          }}
        />
      </div>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px', position: 'relative' }}>
        <Row gutter={[48, 48]} align="middle">
          {/* Content */}
          <Col xs={24} lg={12}>
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Brand Logo */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                style={{ marginBottom: '32px' }}
              >
                <Space size="large" align="center">
                  <div
                    style={{
                      width: '60px',
                      height: '60px',
                      background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                      borderRadius: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
                    }}
                  >
                    <span style={{ color: 'white', fontSize: '28px', fontWeight: 'bold' }}>B</span>
                  </div>
                  <div>
                    <Title level={2} style={{ margin: 0, fontSize: '36px' }}>{BRAND.name}</Title>
                    <Paragraph style={{ margin: 0, color: '#0057ff', fontSize: '18px', fontWeight: 600 }}>
                      {BRAND.tagline}
                    </Paragraph>
                  </div>
                </Space>
              </motion.div>

              {/* Main Heading */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Title
                  level={1}
                  style={{
                    fontSize: 'clamp(2.5rem, 5vw, 4rem)',
                    lineHeight: 1.2,
                    marginBottom: '24px',
                    background: 'linear-gradient(135deg, #000 0%, #333 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  Grow Your Business with{' '}
                  <span style={{
                    background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}>
                    Digital Marketing
                  </span>
                </Title>
              </motion.div>

              {/* Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <Paragraph
                  style={{
                    fontSize: '20px',
                    lineHeight: 1.6,
                    marginBottom: '40px',
                    color: '#666'
                  }}
                >
                  We help businesses like yours increase online visibility, generate more leads,
                  and boost revenue through proven digital marketing strategies that deliver real results.
                </Paragraph>
              </motion.div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                style={{ marginBottom: '48px' }}
              >
                <Space size="large" wrap>
                  <Link to="/contact">
                    <Button
                      type="primary"
                      size="large"
                      icon={<ArrowRightOutlined />}
                      style={{
                        height: '56px',
                        padding: '0 32px',
                        fontSize: '18px',
                        fontWeight: 600,
                        borderRadius: '12px',
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        border: 'none',
                        boxShadow: '0 8px 24px rgba(0, 87, 255, 0.4)',
                        transition: 'all 0.3s ease'
                      }}
                      className="hover:scale-105"
                    >
                      Get Free Audit
                    </Button>
                  </Link>
                  <Button
                    size="large"
                    icon={<PlayCircleOutlined />}
                    style={{
                      height: '56px',
                      padding: '0 32px',
                      fontSize: '18px',
                      fontWeight: 600,
                      borderRadius: '12px',
                      borderColor: '#0057ff',
                      color: '#0057ff'
                    }}
                  >
                    Watch Demo
                  </Button>
                </Space>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
              >
                <Row gutter={[32, 16]} style={{ paddingTop: '32px', borderTop: '1px solid #f0f0f0' }}>
                  <Col xs={8}>
                    <Statistic
                      title="Happy Clients"
                      value={500}
                      suffix="+"
                      valueStyle={{
                        color: '#0057ff',
                        fontSize: '32px',
                        fontWeight: 'bold'
                      }}
                      prefix={<TeamOutlined />}
                    />
                  </Col>
                  <Col xs={8}>
                    <Statistic
                      title="Success Rate"
                      value={98}
                      suffix="%"
                      valueStyle={{
                        color: '#0057ff',
                        fontSize: '32px',
                        fontWeight: 'bold'
                      }}
                      prefix={<TrophyOutlined />}
                    />
                  </Col>
                  <Col xs={8}>
                    <Statistic
                      title="Years Experience"
                      value={5}
                      suffix="+"
                      valueStyle={{
                        color: '#0057ff',
                        fontSize: '32px',
                        fontWeight: 'bold'
                      }}
                      prefix={<CalendarOutlined />}
                    />
                  </Col>
                </Row>
              </motion.div>
            </motion.div>
          </Col>

          {/* Hero Image */}
          <Col xs={24} lg={12}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              style={{ position: 'relative' }}
            >
              <div style={{ position: 'relative', zIndex: 10 }}>
                <img
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                  alt="Digital Marketing Dashboard"
                  style={{
                    width: '100%',
                    height: 'auto',
                    borderRadius: '20px',
                    boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </div>

              {/* Floating Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.2 }}
                style={{
                  position: 'absolute',
                  top: '-20px',
                  left: '-20px',
                  display: window.innerWidth > 768 ? 'block' : 'none'
                }}
              >
                <Card
                  size="small"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
                    border: 'none'
                  }}
                >
                  <Space>
                    <div style={{ width: '8px', height: '8px', background: '#52c41a', borderRadius: '50%' }} />
                    <span style={{ fontWeight: 600 }}>Traffic +250%</span>
                  </Space>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.4 }}
                style={{
                  position: 'absolute',
                  bottom: '-20px',
                  right: '-20px',
                  display: window.innerWidth > 768 ? 'block' : 'none'
                }}
              >
                <Card
                  size="small"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
                    border: 'none'
                  }}
                >
                  <Space>
                    <div style={{ width: '8px', height: '8px', background: '#1890ff', borderRadius: '50%' }} />
                    <span style={{ fontWeight: 600 }}>ROI +180%</span>
                  </Space>
                </Card>
              </motion.div>
            </motion.div>
          </Col>
        </Row>
      </div>
    </section>
  );
};

export default Hero;
