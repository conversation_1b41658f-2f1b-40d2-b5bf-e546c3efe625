import { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

const Input = forwardRef(({ 
  className, 
  type = 'text',
  variant = 'default',
  size = 'md',
  error = false,
  animate = true,
  ...props 
}, ref) => {
  const baseClasses = 'w-full rounded-xl border transition-all duration-200 focus-modern disabled:cursor-not-allowed disabled:opacity-50';
  
  const variants = {
    default: 'bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400',
    filled: 'bg-gray-100 dark:bg-gray-800 border-transparent text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:bg-white dark:focus:bg-gray-900 focus:border-blue-500',
    outline: 'bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400',
  };
  
  const sizes = {
    sm: 'px-3 py-2 text-sm h-9',
    md: 'px-4 py-2.5 text-base h-11',
    lg: 'px-5 py-3 text-lg h-13',
  };
  
  const errorClasses = error 
    ? 'border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500' 
    : 'focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500';
  
  const classes = cn(baseClasses, variants[variant], sizes[size], errorClasses, className);
  
  const InputComponent = (
    <input
      type={type}
      className={classes}
      ref={ref}
      {...props}
    />
  );

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {InputComponent}
      </motion.div>
    );
  }

  return InputComponent;
});

Input.displayName = 'Input';

const Textarea = forwardRef(({ 
  className, 
  variant = 'default',
  size = 'md',
  error = false,
  animate = true,
  rows = 4,
  ...props 
}, ref) => {
  const baseClasses = 'w-full rounded-xl border transition-all duration-200 focus-modern disabled:cursor-not-allowed disabled:opacity-50 resize-none';
  
  const variants = {
    default: 'bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400',
    filled: 'bg-gray-100 dark:bg-gray-800 border-transparent text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:bg-white dark:focus:bg-gray-900 focus:border-blue-500',
    outline: 'bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400',
  };
  
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-base',
    lg: 'px-5 py-3 text-lg',
  };
  
  const errorClasses = error 
    ? 'border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500' 
    : 'focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500';
  
  const classes = cn(baseClasses, variants[variant], sizes[size], errorClasses, className);
  
  const TextareaComponent = (
    <textarea
      className={classes}
      rows={rows}
      ref={ref}
      {...props}
    />
  );

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {TextareaComponent}
      </motion.div>
    );
  }

  return TextareaComponent;
});

Textarea.displayName = 'Textarea';

const Label = forwardRef(({ className, ...props }, ref) => (
  <label
    ref={ref}
    className={cn(
      'text-sm font-medium leading-none text-gray-900 dark:text-gray-100 peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
      className
    )}
    {...props}
  />
));
Label.displayName = 'Label';

export { Input, Textarea, Label };
export default Input;
