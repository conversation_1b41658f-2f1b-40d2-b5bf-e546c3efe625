import { Link } from 'react-router-dom';
import { <PERSON>, Col, Button, Typography, Space, Statistic, Card, Badge, Avatar } from 'antd';
import {
  ArrowRightOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  TeamOutlined,
  CalendarOutlined,
  RocketOutlined,
  StarFilled,
  CheckCircleOutlined,
  <PERSON>boltOutlined,
  FireOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import CountUp from 'react-countup';
import Tilt from 'react-parallax-tilt';
import { BRAND } from '../../utils/constants';

const { Title, Paragraph } = Typography;

const Hero = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  return (
    <section
      ref={ref}
      className="relative overflow-hidden bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900 transition-colors duration-300"
      style={{
        paddingTop: '140px', // Adjusted for larger navbar height (32px on desktop)
        paddingBottom: '120px',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center'
      }}
    >
      {/* Enhanced Background Pattern */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
      >
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute"
          style={{
            top: '10%',
            left: '10%',
            width: '500px',
            height: '500px',
            background: 'radial-gradient(circle, rgba(99,102,241,0.2) 0%, rgba(139,92,246,0.1) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(80px)'
          }}
          animate={{
            scale: [1, 1.3, 1],
            rotate: [0, 180, 360],
            x: [0, 30, 0],
            y: [0, -20, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute"
          style={{
            top: '20%',
            right: '10%',
            width: '400px',
            height: '400px',
            background: 'radial-gradient(circle, rgba(236,72,153,0.18) 0%, rgba(249,115,22,0.08) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(90px)'
          }}
          animate={{
            scale: [1.2, 0.8, 1.2],
            rotate: [360, 180, 0],
            x: [0, -40, 0],
            y: [0, 30, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute"
          style={{
            bottom: '10%',
            left: '20%',
            width: '350px',
            height: '350px',
            background: 'radial-gradient(circle, rgba(16,185,129,0.15) 0%, rgba(6,182,212,0.08) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(75px)',
          }}
          animate={{
            scale: [1, 1.4, 1],
            x: [0, 60, 0],
            y: [0, -40, 0],
            rotate: [0, 120, 240, 360]
          }}
          transition={{
            duration: 22,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Additional colorful orb */}
        <motion.div
          className="absolute"
          style={{
            top: '50%',
            right: '30%',
            width: '280px',
            height: '280px',
            background: 'radial-gradient(circle, rgba(245,158,11,0.12) 0%, rgba(239,68,68,0.06) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(65px)',
          }}
          animate={{
            scale: [1.1, 0.9, 1.1],
            rotate: [0, -180, -360],
            x: [0, -25, 0],
            y: [0, 25, 0]
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating particles with multiple colors */}
        {[...Array(25)].map((_, i) => {
          const colors = [
            'bg-purple-400', 'bg-pink-400', 'bg-blue-400', 'bg-indigo-400',
            'bg-green-400', 'bg-teal-400', 'bg-orange-400', 'bg-yellow-400'
          ];
          const shapes = ['rounded-full', 'rounded-sm', 'rounded-md'];
          return (
            <motion.div
              key={i}
              className={`absolute w-3 h-3 ${colors[i % colors.length]} ${shapes[i % shapes.length]} opacity-30`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -120, 0],
                x: [0, Math.random() * 60 - 30, 0],
                opacity: [0.3, 0.8, 0.3],
                scale: [1, 1.8, 1],
                rotate: [0, 360, 0]
              }}
              transition={{
                duration: 10 + Math.random() * 5,
                repeat: Infinity,
                delay: Math.random() * 6,
                ease: "easeInOut"
              }}
            />
          );
        })}

        {/* Enhanced grid pattern overlay */}
        <div
          className="absolute inset-0 opacity-8"
          style={{
            backgroundImage: `
              linear-gradient(rgba(99,102,241,0.12) 1px, transparent 1px),
              linear-gradient(90deg, rgba(139,92,246,0.12) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px',
            animation: 'float 25s ease-in-out infinite'
          }}
        />

        {/* Animated geometric shapes */}
        <motion.div
          className="absolute top-32 right-1/4 w-20 h-20 border-3 border-purple-300 opacity-25"
          style={{ borderRadius: '30%' }}
          animate={{
            rotate: [0, 360],
            scale: [1, 1.3, 1],
            borderRadius: ['30%', '50%', '30%']
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-40 right-20 w-16 h-16 bg-gradient-to-r from-pink-300 via-purple-300 to-indigo-300 opacity-30"
          style={{ borderRadius: '50%' }}
          animate={{
            y: [0, -30, 0],
            scale: [1, 1.4, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute top-1/2 left-20 w-12 h-12 bg-gradient-to-br from-green-300 to-teal-300 opacity-25"
          style={{ borderRadius: '25%' }}
          animate={{
            x: [0, 40, 0],
            y: [0, -20, 0],
            rotate: [0, -180, -360],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.div>

      <div className="container-custom relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <Row gutter={[64, 64]} align="middle">
            {/* Enhanced Content */}
            <Col xs={24} lg={12}>
              <motion.div variants={itemVariants}>
                {/* Enhanced Brand Badge */}
                <motion.div
                  variants={itemVariants}
                  className="mb-8"
                >
                  <Badge.Ribbon
                    text={
                      <span className="flex items-center gap-1">
                        <StarFilled className="text-yellow-400" />
                        <span className="font-semibold">Top Rated Agency</span>
                      </span>
                    }
                    color="#0057ff"
                    placement="start"
                  >
                    <Card
                      className="inline-block glass border-0"
                      style={{
                        background: 'rgba(255, 255, 255, 0.9)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '16px',
                        padding: '8px 16px'
                      }}
                    >
                      <Space size="middle" align="center">
                        <motion.div
                          className="relative"
                          whileHover={{
                            rotate: [0, -10, 10, 0],
                            scale: 1.1
                          }}
                          transition={{ duration: 0.5 }}
                        >
                          <div
                            className="w-14 h-14 rounded-2xl flex items-center justify-center relative overflow-hidden"
                            style={{
                              background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 50%, #40a9ff 100%)',
                              boxShadow: '0 12px 32px rgba(0, 87, 255, 0.4)'
                            }}
                          >
                            <span className="text-white font-bold text-2xl relative z-10">B</span>

                            {/* Animated shine effect */}
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
                              animate={{
                                x: ['-100%', '100%']
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                repeatDelay: 3
                              }}
                            />
                          </div>

                          {/* Floating sparkles */}
                          {[...Array(3)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-1 h-1 bg-yellow-400 rounded-full"
                              style={{
                                top: `${20 + i * 15}%`,
                                right: `${10 + i * 10}%`,
                              }}
                              animate={{
                                opacity: [0, 1, 0],
                                scale: [0, 1, 0],
                                y: [0, -10, 0],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                delay: i * 0.5,
                              }}
                            />
                          ))}
                        </motion.div>

                        <div>
                          <Title
                            level={2}
                            className="gradient-text m-0"
                            style={{
                              fontSize: '28px',
                              background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                              WebkitBackgroundClip: 'text',
                              WebkitTextFillColor: 'transparent'
                            }}
                          >
                            {BRAND.name}
                          </Title>
                          <motion.div
                            className="text-blue-600 dark:text-blue-400 font-semibold text-lg"
                            animate={{
                              opacity: [0.7, 1, 0.7]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity
                            }}
                          >
                            {BRAND.tagline}
                          </motion.div>
                        </div>
                      </Space>
                    </Card>
                  </Badge.Ribbon>
                </motion.div>

                {/* Enhanced Main Heading */}
                <motion.div variants={itemVariants} className="mb-12">
                  <Title
                    level={1}
                    className="text-hero mb-6"
                    style={{
                      lineHeight: 1.1,
                      marginBottom: '40px',
                      fontSize: 'clamp(3rem, 6vw, 5.5rem)',
                      fontWeight: 900
                    }}
                  >
                    <motion.span
                      className="block relative"
                      style={{
                        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundSize: '200% 100%'
                      }}
                      animate={{
                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                      }}
                      transition={{
                        duration: 6,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    >
                      Transform Your Business
                      <motion.div
                        className="absolute -inset-2 bg-gradient-to-r from-purple-300/20 via-pink-300/20 to-blue-300/20 rounded-lg opacity-0"
                        animate={{
                          opacity: [0, 0.3, 0],
                          scale: [0.95, 1.05, 0.95]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    </motion.span>

                    <motion.span
                      className="block relative"
                      style={{
                        background: 'linear-gradient(135deg, #f97316 0%, #10b981 50%, #06b6d4 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundSize: '200% 100%'
                      }}
                      animate={{
                        backgroundPosition: ['100% 50%', '0% 50%', '100% 50%']
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    >
                      with Digital Excellence ✨
                      <motion.div
                        className="absolute -inset-2 bg-gradient-to-r from-orange-300/20 via-green-300/20 to-cyan-300/20 rounded-lg opacity-0"
                        animate={{
                          opacity: [0, 0.4, 0],
                          scale: [0.95, 1.05, 0.95]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 1
                        }}
                      />
                    </motion.span>

                    {/* Enhanced animated underline */}
                    <motion.div
                      className="h-3 bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 to-green-500 rounded-full mt-6 relative overflow-hidden"
                      initial={{ width: 0 }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 2, delay: 1.5 }}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent"
                        animate={{
                          x: ['-100%', '100%']
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "linear",
                          delay: 3
                        }}
                      />
                    </motion.div>
                  </Title>
                </motion.div>

                {/* Enhanced Description */}
                <motion.div variants={itemVariants} className="mb-10">
                  <Paragraph
                    className="text-xl leading-relaxed mb-8 text-gray-700 dark:text-gray-300"
                    style={{
                      fontSize: '24px',
                      lineHeight: 1.8,
                      marginBottom: '56px',
                      maxWidth: '650px',
                      fontWeight: 500
                    }}
                  >
                    We help ambitious businesses like yours{' '}
                    <motion.span
                      className="font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                      whileHover={{ scale: 1.08 }}
                      style={{ position: 'relative' }}
                    >
                      🚀 skyrocket their online presence
                    </motion.span>
                    , generate high-quality leads, and{' '}
                    <motion.span
                      className="font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"
                      whileHover={{ scale: 1.08 }}
                    >
                      💰 boost revenue by 300%+
                    </motion.span>
                    {' '}through data-driven digital marketing strategies that deliver{' '}
                    <motion.span
                      className="font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"
                      whileHover={{ scale: 1.08 }}
                    >
                      📈 measurable results
                    </motion.span>
                    .
                  </Paragraph>

                  {/* Enhanced Key benefits */}
                  <motion.div
                    className="flex flex-wrap gap-5 mb-12"
                    variants={containerVariants}
                  >
                    {[
                      { icon: "✅", text: "Guaranteed ROI", gradient: "from-green-500 to-emerald-500", bg: "from-green-50 to-emerald-50" },
                      { icon: "⚡", text: "Fast Results", gradient: "from-yellow-500 to-orange-500", bg: "from-yellow-50 to-orange-50" },
                      { icon: "🔥", text: "Expert Team", gradient: "from-red-500 to-pink-500", bg: "from-red-50 to-pink-50" },
                      { icon: "🎯", text: "Targeted Strategy", gradient: "from-blue-500 to-purple-500", bg: "from-blue-50 to-purple-50" }
                    ].map((benefit, index) => (
                      <motion.div
                        key={index}
                        variants={itemVariants}
                        className={`flex items-center gap-3 px-6 py-4 bg-gradient-to-r ${benefit.bg} rounded-2xl shadow-lg border border-white/50 backdrop-blur-sm`}
                        whileHover={{
                          scale: 1.08,
                          y: -2,
                          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.15)'
                        }}
                        transition={{ duration: 0.2 }}
                      >
                        <motion.span
                          className="text-2xl"
                          animate={{
                            rotate: [0, 10, -10, 0],
                            scale: [1, 1.1, 1]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            delay: index * 0.5
                          }}
                        >
                          {benefit.icon}
                        </motion.span>
                        <span className={`font-bold text-lg bg-gradient-to-r ${benefit.gradient} bg-clip-text text-transparent`}>
                          {benefit.text}
                        </span>
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>

                {/* Enhanced CTA Buttons */}
                <motion.div
                  variants={itemVariants}
                  className="mb-12"
                >
                  <Space size="large" wrap className="w-full">
                    {/* Primary CTA */}
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link to="/contact">
                        <Button
                          type="primary"
                          size="large"
                          icon={<RocketOutlined />}
                          className="btn-gradient hover-glow"
                          style={{
                            height: '64px',
                            padding: '0 40px',
                            fontSize: '20px',
                            fontWeight: 700,
                            borderRadius: '16px',
                            background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                            border: 'none',
                            boxShadow: '0 12px 32px rgba(0, 87, 255, 0.4)',
                            position: 'relative',
                            overflow: 'hidden'
                          }}
                        >
                          <span className="relative z-10 flex items-center gap-2">
                            Get Free Audit
                            <motion.span
                              animate={{ x: [0, 5, 0] }}
                              transition={{ duration: 1.5, repeat: Infinity }}
                            >
                              →
                            </motion.span>
                          </span>

                          {/* Animated background */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"
                            initial={{ x: '-100%' }}
                            whileHover={{ x: '0%' }}
                            transition={{ duration: 0.3 }}
                          />
                        </Button>
                      </Link>
                    </motion.div>

                    {/* Secondary CTA */}
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        size="large"
                        icon={<PlayCircleOutlined />}
                        className="glass hover-lift"
                        style={{
                          height: '64px',
                          padding: '0 40px',
                          fontSize: '20px',
                          fontWeight: 600,
                          borderRadius: '16px',
                          borderColor: '#0057ff',
                          color: '#0057ff',
                          background: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        <span className="flex items-center gap-2">
                          Watch Demo
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            ▶
                          </motion.div>
                        </span>
                      </Button>
                    </motion.div>
                  </Space>

                  {/* Trust indicators */}
                  <motion.div
                    className="mt-6 flex items-center gap-6 text-sm text-gray-500"
                    variants={itemVariants}
                  >
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <span>No setup fees</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <span>Cancel anytime</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <span>Results in 30 days</span>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Enhanced Stats */}
                <motion.div variants={itemVariants}>
                  <div className="pt-8 border-t border-gray-200">
                    <Row gutter={[32, 24]}>
                      {[
                        {
                          title: "Happy Clients",
                          value: 500,
                          suffix: "+",
                          icon: <TeamOutlined />,
                          color: "#0057ff",
                          description: "Satisfied customers worldwide"
                        },
                        {
                          title: "Success Rate",
                          value: 98,
                          suffix: "%",
                          icon: <TrophyOutlined />,
                          color: "#10b981",
                          description: "Project completion rate"
                        },
                        {
                          title: "Years Experience",
                          value: 5,
                          suffix: "+",
                          icon: <CalendarOutlined />,
                          color: "#f59e0b",
                          description: "In digital marketing"
                        }
                      ].map((stat, index) => (
                        <Col xs={8} key={index}>
                          <motion.div
                            className="text-center"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.2 }}
                          >
                            <motion.div
                              className="inline-flex items-center justify-center w-12 h-12 rounded-xl mb-3"
                              style={{
                                background: `${stat.color}15`,
                                color: stat.color
                              }}
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.5 }}
                            >
                              {stat.icon}
                            </motion.div>

                            <div className="text-3xl font-bold mb-1" style={{ color: stat.color }}>
                              {inView && (
                                <CountUp
                                  end={stat.value}
                                  duration={2.5}
                                  delay={index * 0.2}
                                />
                              )}
                              {stat.suffix}
                            </div>

                            <div className="text-gray-900 font-semibold text-lg mb-1">
                              {stat.title}
                            </div>

                            <div className="text-gray-500 text-sm">
                              {stat.description}
                            </div>
                          </motion.div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </motion.div>
              </motion.div>
            </Col>

            {/* Enhanced Hero Image */}
            <Col xs={24} lg={12}>
              <motion.div
                variants={itemVariants}
                className="relative"
              >
                <Tilt
                  tiltMaxAngleX={5}
                  tiltMaxAngleY={5}
                  perspective={1000}
                  scale={1.02}
                  transitionSpeed={1000}
                  gyroscope={true}
                >
                  <div className="relative z-10">
                    <motion.div
                      className="relative overflow-hidden rounded-3xl"
                      style={{
                        boxShadow: '0 25px 60px rgba(0, 0, 0, 0.15)'
                      }}
                      whileHover={{
                        boxShadow: '0 35px 80px rgba(0, 87, 255, 0.2)'
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                        alt="Digital Marketing Dashboard"
                        className="w-full h-auto"
                        style={{
                          borderRadius: '24px',
                          filter: 'brightness(1.1) contrast(1.1)'
                        }}
                      />

                      {/* Overlay gradient */}
                      <div
                        className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 to-purple-500/10 rounded-3xl"
                        style={{ mixBlendMode: 'overlay' }}
                      />

                      {/* Animated border */}
                      <motion.div
                        className="absolute inset-0 rounded-3xl"
                        style={{
                          background: 'linear-gradient(45deg, #0057ff, #1890ff, #40a9ff, #0057ff)',
                          backgroundSize: '300% 300%',
                          padding: '2px'
                        }}
                        animate={{
                          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      >
                        <div className="w-full h-full bg-white rounded-3xl" />
                      </motion.div>
                    </motion.div>
                  </div>
                </Tilt>

                {/* Enhanced Floating Cards */}
                <motion.div
                  className="absolute -top-6 -left-6 hidden lg:block"
                  initial={{ opacity: 0, y: 20, rotate: -5 }}
                  animate={{ opacity: 1, y: 0, rotate: 0 }}
                  transition={{ duration: 0.8, delay: 1.2 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <Card
                    className="glass border-0"
                    style={{
                      borderRadius: '16px',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      boxShadow: '0 12px 32px rgba(0, 0, 0, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <Space align="center">
                      <motion.div
                        className="w-3 h-3 bg-green-500 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <span className="font-bold text-green-600">Traffic +250%</span>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        📈
                      </motion.div>
                    </Space>
                  </Card>
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -right-6 hidden lg:block"
                  initial={{ opacity: 0, y: 20, rotate: 5 }}
                  animate={{ opacity: 1, y: 0, rotate: 0 }}
                  transition={{ duration: 0.8, delay: 1.4 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: -5,
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <Card
                    className="glass border-0"
                    style={{
                      borderRadius: '16px',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      boxShadow: '0 12px 32px rgba(0, 0, 0, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <Space align="center">
                      <motion.div
                        className="w-3 h-3 bg-blue-500 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                      />
                      <span className="font-bold text-blue-600">ROI +180%</span>
                      <motion.div
                        animate={{ rotate: -360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        💰
                      </motion.div>
                    </Space>
                  </Card>
                </motion.div>

                {/* Additional floating elements */}
                <motion.div
                  className="absolute top-1/2 -right-12 hidden xl:block"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 1.6 }}
                >
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-xl"
                    animate={{
                      y: [0, -10, 0],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    whileHover={{ scale: 1.2 }}
                  >
                    🚀
                  </motion.div>
                </motion.div>

                <motion.div
                  className="absolute top-1/4 -left-8 hidden xl:block"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 1.8 }}
                >
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-white font-bold"
                    animate={{
                      y: [0, 10, 0],
                      rotate: [0, -5, 5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                    whileHover={{ scale: 1.2 }}
                  >
                    ⭐
                  </motion.div>
                </motion.div>
              </motion.div>
            </Col>
          </Row>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
