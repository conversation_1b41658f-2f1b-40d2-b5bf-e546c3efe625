import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import {
  Carousel,
  Card,
  Rate,
  Typography,
  Avatar,
  Space,
  Button,
  Row,
  Col,
  Badge,
  Divider
} from 'antd';
import {
  LeftOutlined,
  RightOutlined,
  StarFilled,
  MessageOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UserOutlined,
  CrownOutlined,
  HeartFilled,
  CheckCircleOutlined
} from '@ant-design/icons';
import Tilt from 'react-parallax-tilt';
import { TESTIMONIALS } from '../../utils/constants';

const { Title, Paragraph, Text } = Typography;

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const carouselRef = useRef(null);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      if (carouselRef.current) {
        carouselRef.current.next();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const handleSlideChange = (current) => {
    setCurrentIndex(current);
  };

  const goToPrevious = () => {
    setIsAutoPlaying(false);
    if (carouselRef.current) {
      carouselRef.current.prev();
    }
  };

  const goToNext = () => {
    setIsAutoPlaying(false);
    if (carouselRef.current) {
      carouselRef.current.next();
    }
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  return (
    <section
      ref={ref}
      className="section-padding relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f0f9ff 100%)'
      }}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-20 left-20 w-40 h-40 rounded-full opacity-10"
          style={{
            background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
            filter: 'blur(60px)'
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-20 right-20 w-32 h-32 rounded-full opacity-10"
          style={{
            background: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',
            filter: 'blur(50px)'
          }}
          animate={{
            y: [0, 25, 0],
            x: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Floating testimonial icons */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              fontSize: '24px'
            }}
            animate={{
              y: [0, -20, 0],
              rotate: [0, 360],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 6 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
          >
            {i % 2 === 0 ? '⭐' : '💬'}
          </motion.div>
        ))}
      </div>

      <div className="container-custom relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Enhanced Section Header */}
          <motion.div
            variants={itemVariants}
            className="text-center mb-16"
          >
            {/* Badge */}
            <motion.div
              className="inline-block mb-6"
              whileHover={{ scale: 1.05 }}
            >
              <Badge.Ribbon
                text={
                  <span className="flex items-center gap-1">
                    <HeartFilled className="text-red-300" />
                    <span className="font-semibold">Client Love</span>
                  </span>
                }
                color="#0057ff"
              >
                <Card
                  className="glass border-0"
                  style={{
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '12px',
                    padding: '8px 16px'
                  }}
                >
                  <Space align="center">
                    <motion.div
                      animate={{
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity
                      }}
                    >
                      <StarFilled className="text-yellow-500 text-lg" />
                    </motion.div>
                    <span className="font-semibold text-gray-700">Testimonials</span>
                  </Space>
                </Card>
              </Badge.Ribbon>
            </motion.div>

            <Title
              level={2}
              className="text-display mb-6"
              style={{
                background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              What Our Clients Say
            </Title>

            <Paragraph
              className="text-xl leading-relaxed text-gray-600 max-w-3xl mx-auto"
              style={{ fontSize: '20px', lineHeight: 1.7 }}
            >
              Don't just take our word for it. Here's what our{' '}
              <motion.span
                className="font-semibold text-blue-600"
                whileHover={{ scale: 1.05 }}
              >
                satisfied clients
              </motion.span>
              {' '}have to say about working with us and the{' '}
              <motion.span
                className="font-semibold text-green-600"
                whileHover={{ scale: 1.05 }}
              >
                amazing results
              </motion.span>
              {' '}we've achieved together.
            </Paragraph>

            {/* Trust indicators */}
            <motion.div
              className="flex justify-center items-center gap-8 mt-8"
              variants={itemVariants}
            >
              {[
                { icon: <CheckCircleOutlined />, text: "500+ Reviews", color: "#10b981" },
                { icon: <StarFilled />, text: "4.9/5 Rating", color: "#f59e0b" },
                { icon: <CrownOutlined />, text: "Top Rated Agency", color: "#8b5cf6" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-2 px-4 py-2 bg-white rounded-full shadow-md border border-gray-100"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  <span style={{ color: stat.color, fontSize: '16px' }}>
                    {stat.icon}
                  </span>
                  <span className="font-medium text-gray-700 text-sm">{stat.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Enhanced Testimonials Carousel */}
          <motion.div
            variants={itemVariants}
            className="relative max-w-5xl mx-auto"
          >
            <div className="relative">
              <Carousel
                ref={carouselRef}
                autoplay={isAutoPlaying}
                autoplaySpeed={5000}
                dots={false}
                effect="fade"
                afterChange={handleSlideChange}
                className="testimonials-carousel"
              >
                {TESTIMONIALS.map((testimonial, index) => (
                  <div key={testimonial.id}>
                    <Tilt
                      tiltMaxAngleX={2}
                      tiltMaxAngleY={2}
                      perspective={1000}
                      scale={1.01}
                    >
                      <Card
                        className="border-0 relative overflow-hidden mx-4"
                        style={{
                          borderRadius: '32px',
                          background: 'rgba(255, 255, 255, 0.95)',
                          backdropFilter: 'blur(15px)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)'
                        }}
                        bodyStyle={{ padding: '48px 40px' }}
                      >
                        {/* Background decorations */}
                        <div className="absolute inset-0 pointer-events-none">
                          {/* Quote background */}
                          <motion.div
                            className="absolute top-8 left-8 opacity-10"
                            animate={{
                              rotate: [0, 5, -5, 0],
                              scale: [1, 1.1, 1]
                            }}
                            transition={{
                              duration: 4,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          >
                            <MessageOutlined style={{ fontSize: '64px', color: '#0057ff' }} />
                          </motion.div>

                          {/* Floating sparkles */}
                          {[...Array(5)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-1 h-1 bg-yellow-400 rounded-full opacity-60"
                              style={{
                                left: `${20 + i * 15}%`,
                                top: `${15 + i * 10}%`,
                              }}
                              animate={{
                                opacity: [0.6, 1, 0.6],
                                scale: [1, 1.5, 1],
                                y: [0, -10, 0],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                delay: i * 0.4,
                              }}
                            />
                          ))}
                        </div>

                        <div className="text-center relative z-10">
                          {/* Rating */}
                          <motion.div
                            className="mb-6"
                            whileHover={{ scale: 1.1 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Rate
                              disabled
                              value={testimonial.rating}
                              style={{ fontSize: '24px', color: '#faad14' }}
                            />
                          </motion.div>

                          {/* Testimonial Content */}
                          <motion.blockquote
                            className="text-xl md:text-2xl text-gray-700 mb-8 leading-relaxed italic font-medium"
                            style={{ lineHeight: 1.6 }}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                          >
                            "{testimonial.content}"
                          </motion.blockquote>

                          <Divider style={{ margin: '32px 0', borderColor: '#e5e7eb' }} />

                          {/* Client Info */}
                          <motion.div
                            className="flex flex-col items-center"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.4 }}
                          >
                            <motion.div
                              className="relative mb-4"
                              whileHover={{ scale: 1.1, rotate: 5 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Avatar
                                size={80}
                                icon={<UserOutlined />}
                                style={{
                                  background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                                  border: '4px solid white',
                                  boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)',
                                  fontSize: '32px'
                                }}
                              >
                                {testimonial.name.charAt(0)}
                              </Avatar>

                              {/* Verified badge */}
                              <motion.div
                                className="absolute -bottom-1 -right-1"
                                animate={{
                                  scale: [1, 1.2, 1]
                                }}
                                transition={{
                                  duration: 2,
                                  repeat: Infinity
                                }}
                              >
                                <Badge
                                  count={<CheckCircleOutlined style={{ color: '#10b981' }} />}
                                  style={{ backgroundColor: 'transparent' }}
                                />
                              </motion.div>
                            </motion.div>

                            <Title level={4} className="m-0 mb-1 text-gray-900">
                              {testimonial.name}
                            </Title>

                            <Text
                              className="text-blue-600 font-semibold text-lg mb-1"
                              style={{ fontSize: '16px' }}
                            >
                              {testimonial.role}
                            </Text>

                            <Text className="text-gray-500 font-medium">
                              {testimonial.company}
                            </Text>
                          </motion.div>
                        </div>
                      </Card>
                    </Tilt>
                  </div>
                ))}
              </Carousel>

              {/* Custom Navigation Buttons */}
              <motion.div
                className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-6 hidden lg:block"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Button
                  type="primary"
                  shape="circle"
                  size="large"
                  icon={<LeftOutlined />}
                  onClick={goToPrevious}
                  className="glass"
                  style={{
                    width: '56px',
                    height: '56px',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 87, 255, 0.2)',
                    color: '#0057ff',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </motion.div>

              <motion.div
                className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-6 hidden lg:block"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Button
                  type="primary"
                  shape="circle"
                  size="large"
                  icon={<RightOutlined />}
                  onClick={goToNext}
                  className="glass"
                  style={{
                    width: '56px',
                    height: '56px',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 87, 255, 0.2)',
                    color: '#0057ff',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </motion.div>
            </div>
          </motion.div>

          {/* Enhanced Controls */}
          <motion.div
            variants={itemVariants}
            className="flex justify-center items-center gap-6 mt-12"
          >
            {/* Dots Indicator */}
            <div className="flex items-center gap-3">
              {TESTIMONIALS.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => {
                    setIsAutoPlaying(false);
                    carouselRef.current?.goTo(index);
                  }}
                  className={`transition-all duration-300 rounded-full ${
                    index === currentIndex
                      ? 'w-8 h-3 bg-blue-500'
                      : 'w-3 h-3 bg-gray-300 hover:bg-gray-400'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            {/* Auto-play Control */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                type="text"
                icon={isAutoPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={toggleAutoPlay}
                className="flex items-center gap-2"
                style={{
                  color: '#0057ff',
                  fontWeight: 500,
                  height: '40px',
                  padding: '0 16px',
                  borderRadius: '20px'
                }}
              >
                {isAutoPlaying ? 'Pause' : 'Play'}
              </Button>
            </motion.div>
          </motion.div>

          {/* Additional Stats */}
          <motion.div
            variants={itemVariants}
            className="mt-16"
          >
            <Row gutter={[32, 32]} justify="center">
              {[
                { title: 'Client Satisfaction', value: '98%', icon: <HeartFilled />, color: '#ef4444' },
                { title: 'Repeat Clients', value: '85%', icon: <CheckCircleOutlined />, color: '#10b981' },
                { title: 'Avg Project Rating', value: '4.9/5', icon: <StarFilled />, color: '#f59e0b' },
                { title: 'Response Time', value: '<2hrs', icon: <MessageOutlined />, color: '#8b5cf6' }
              ].map((stat, index) => (
                <Col xs={12} sm={6} key={index}>
                  <motion.div
                    whileHover={{ scale: 1.05, y: -4 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card
                      className="text-center border-0 hover-lift"
                      style={{
                        borderRadius: '16px',
                        background: 'rgba(255, 255, 255, 0.8)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.2)'
                      }}
                      bodyStyle={{ padding: '20px 16px' }}
                    >
                      <motion.div
                        className="inline-flex items-center justify-center w-12 h-12 rounded-xl mb-3"
                        style={{
                          background: `${stat.color}15`,
                          color: stat.color
                        }}
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.5 }}
                      >
                        {stat.icon}
                      </motion.div>

                      <div
                        className="text-xl font-bold mb-1"
                        style={{ color: stat.color }}
                      >
                        {stat.value}
                      </div>

                      <div className="text-gray-600 text-sm font-medium">
                        {stat.title}
                      </div>
                    </Card>
                  </motion.div>
                </Col>
              ))}
            </Row>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
