import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Layout, Menu, But<PERSON>, Drawer, Space, Badge } from 'antd';
import { MenuOutlined, PhoneOutlined, RocketOutlined, StarFilled } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_LINKS, BRAND } from '../../utils/constants';

const { Header } = Layout;

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Show/hide navbar based on scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }

      // Add glassmorphism effect when scrolled
      setIsScrolled(currentScrollY > 10);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  // Convert nav links to Ant Design menu items with enhanced styling
  const menuItems = NAV_LINKS.map((link) => ({
    key: link.path,
    label: (
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.2 }}
      >
        <Link
          to={link.path}
          style={{
            color: location.pathname === link.path ? '#0057ff' : '#374151',
            fontWeight: location.pathname === link.path ? 700 : 500,
            fontSize: '16px',
            textDecoration: 'none',
            position: 'relative',
            padding: '8px 16px',
            borderRadius: '8px',
            transition: 'all 0.3s ease',
            background: location.pathname === link.path
              ? 'linear-gradient(135deg, rgba(0, 87, 255, 0.1) 0%, rgba(24, 144, 255, 0.1) 100%)'
              : 'transparent'
          }}
          className={location.pathname === link.path ? 'active-nav-link' : 'nav-link'}
        >
          {link.name}
        </Link>
      </motion.div>
    ),
  }));

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          className="fixed top-0 left-0 right-0 z-50"
        >
          <Header
            className={`transition-all duration-500 ease-out ${
              isScrolled ? 'shadow-2xl' : 'shadow-md'
            }`}
            style={{
              background: isScrolled
                ? 'rgba(255, 255, 255, 0.9)'
                : 'rgba(255, 255, 255, 0.95)',
              backdropFilter: isScrolled ? 'blur(20px)' : 'blur(15px)',
              WebkitBackdropFilter: isScrolled ? 'blur(20px)' : 'blur(15px)',
              border: isScrolled
                ? '1px solid rgba(0, 87, 255, 0.1)'
                : '1px solid rgba(255, 255, 255, 0.2)',
              borderTop: 'none',
              borderLeft: 'none',
              borderRight: 'none',
              borderBottom: isScrolled ? '1px solid rgba(0, 87, 255, 0.1)' : 'none',
              padding: '0 24px',
              height: '80px',
              lineHeight: '80px',
              position: 'relative',
              overflow: 'hidden',
              boxShadow: isScrolled
                ? '0 8px 32px rgba(0, 87, 255, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)'
                : '0 4px 16px rgba(0, 0, 0, 0.05)'
            }}
          >
            {/* Animated background gradient */}
            <div
              className="absolute inset-0 opacity-5 pointer-events-none"
              style={{
                background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 50%, #40a9ff 100%)',
                animation: 'pulse 4s ease-in-out infinite'
              }}
            />

            {/* Floating particles effect */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-20"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    y: [0, -20, 0],
                    opacity: [0.2, 0.5, 0.2],
                  }}
                  transition={{
                    duration: 3 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                  }}
                />
              ))}
            </div>
            <div className="max-w-7xl mx-auto flex justify-between items-center h-full relative z-10">
              {/* Enhanced Logo */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <Link to="/" className="flex items-center space-x-3" onClick={closeMenu}>
                  <div className="relative">
                    <motion.div
                      className="w-12 h-12 rounded-xl flex items-center justify-center relative overflow-hidden"
                      style={{
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
                      }}
                      whileHover={{
                        boxShadow: '0 12px 32px rgba(0, 87, 255, 0.4)',
                        rotate: [0, -5, 5, 0],
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <span className="text-white font-bold text-xl relative z-10">B</span>

                      {/* Animated background */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0"
                        whileHover={{ opacity: 0.3 }}
                        transition={{ duration: 0.3 }}
                      />

                      {/* Sparkle effect */}
                      <motion.div
                        className="absolute top-1 right-1 w-1 h-1 bg-white rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          scale: [0, 1, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: 1,
                        }}
                      />
                    </motion.div>

                    {/* Floating badge */}
                    <motion.div
                      className="absolute -top-2 -right-2"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5, type: 'spring', stiffness: 500 }}
                    >
                      <Badge
                        count={<StarFilled style={{ color: '#faad14', fontSize: '10px' }} />}
                        style={{
                          backgroundColor: 'transparent',
                          boxShadow: 'none',
                          border: 'none'
                        }}
                      />
                    </motion.div>
                  </div>

                  <div className="flex flex-col">
                    <motion.span
                      className="text-2xl font-bold gradient-text"
                      style={{
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text'
                      }}
                      whileHover={{ scale: 1.02 }}
                    >
                      {BRAND.name}
                    </motion.span>
                    <motion.span
                      className="text-xs text-gray-500 font-medium -mt-1"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      Digital Excellence
                    </motion.span>
                  </div>
                </Link>
              </motion.div>

              {/* Enhanced Desktop Navigation */}
              <div className="hidden lg:flex items-center">
                <Menu
                  mode="horizontal"
                  selectedKeys={[location.pathname]}
                  items={menuItems}
                  style={{
                    border: 'none',
                    background: 'transparent',
                    fontSize: '16px',
                    fontWeight: 500,
                    minWidth: 'auto'
                  }}
                  className="flex-1 nav-menu"
                />

                <Space size="large" style={{ marginLeft: '32px' }}>
                  {/* Enhanced CTA Button */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link to="/contact">
                      <Button
                        type="primary"
                        size="large"
                        icon={<RocketOutlined />}
                        className="btn-gradient hover-glow"
                        style={{
                          borderRadius: '12px',
                          height: '48px',
                          padding: '0 24px',
                          fontWeight: 700,
                          fontSize: '16px',
                          background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                          border: 'none',
                          boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                      >
                        <span className="relative z-10">Get Free Audit</span>

                        {/* Animated background */}
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 opacity-0"
                          whileHover={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        />
                      </Button>
                    </Link>
                  </motion.div>

                  {/* Additional quick action */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Button
                      type="text"
                      size="large"
                      style={{
                        color: '#0057ff',
                        fontWeight: 600,
                        fontSize: '16px',
                        height: '48px',
                        padding: '0 16px',
                        borderRadius: '12px',
                        transition: 'all 0.3s ease'
                      }}
                      className="hover:bg-blue-50"
                    >
                      <PhoneOutlined style={{ marginRight: '8px' }} />
                      Call Now
                    </Button>
                  </motion.div>
                </Space>
              </div>

              {/* Enhanced Mobile menu button */}
              <div className="lg:hidden">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    type="text"
                    icon={
                      <motion.div
                        animate={{ rotate: isOpen ? 90 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <MenuOutlined />
                      </motion.div>
                    }
                    onClick={toggleMenu}
                    size="large"
                    style={{
                      color: '#0057ff',
                      width: '48px',
                      height: '48px',
                      borderRadius: '12px',
                      background: isOpen ? 'rgba(0, 87, 255, 0.1)' : 'transparent',
                      transition: 'all 0.3s ease'
                    }}
                    className="hover:bg-blue-50"
                  />
                </motion.div>
              </div>
            </div>

            {/* Enhanced Mobile Navigation Drawer */}
            <Drawer
              title={
                <motion.div
                  className="flex items-center space-x-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div
                    className="w-10 h-10 rounded-xl flex items-center justify-center"
                    style={{
                      background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                      boxShadow: '0 4px 12px rgba(0, 87, 255, 0.3)'
                    }}
                  >
                    <span className="text-white font-bold text-lg">B</span>
                  </div>
                  <div>
                    <span className="text-xl font-bold gradient-text">{BRAND.name}</span>
                    <div className="text-xs text-gray-500 font-medium">Digital Excellence</div>
                  </div>
                </motion.div>
              }
              placement="right"
              onClose={closeMenu}
              open={isOpen}
              width={320}
              styles={{
                body: {
                  padding: 0,
                  background: 'linear-gradient(135deg, #f8fafc 0%, #ffffff 100%)'
                },
                header: {
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  borderBottom: '1px solid rgba(0, 87, 255, 0.1)'
                }
              }}
              className="mobile-drawer"
            >
              <div className="p-6">
                {/* Mobile Menu Items */}
                <div className="space-y-2 mb-8">
                  {NAV_LINKS.map((link, index) => (
                    <motion.div
                      key={link.path}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        to={link.path}
                        onClick={closeMenu}
                        className={`block px-4 py-3 rounded-xl text-lg font-medium transition-all duration-300 ${
                          location.pathname === link.path
                            ? 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-600 border-l-4 border-blue-500'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                        }`}
                      >
                        {link.name}
                      </Link>
                    </motion.div>
                  ))}
                </div>

                {/* Mobile CTA Buttons */}
                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <Link to="/contact" onClick={closeMenu}>
                    <Button
                      type="primary"
                      block
                      size="large"
                      icon={<RocketOutlined />}
                      className="btn-gradient"
                      style={{
                        borderRadius: '12px',
                        height: '52px',
                        fontWeight: 700,
                        fontSize: '16px',
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        border: 'none',
                        boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
                      }}
                    >
                      Get Free Audit
                    </Button>
                  </Link>

                  <Button
                    block
                    size="large"
                    icon={<PhoneOutlined />}
                    style={{
                      borderRadius: '12px',
                      height: '52px',
                      fontWeight: 600,
                      fontSize: '16px',
                      borderColor: '#0057ff',
                      color: '#0057ff'
                    }}
                    className="hover:bg-blue-50"
                  >
                    Call Now
                  </Button>
                </motion.div>

                {/* Contact Info */}
                <motion.div
                  className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                >
                  <div className="text-sm text-gray-600 mb-2">Quick Contact</div>
                  <div className="text-blue-600 font-semibold">{BRAND.phone}</div>
                  <div className="text-blue-600 font-semibold">{BRAND.email}</div>
                </motion.div>
              </div>
            </Drawer>
          </Header>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Navbar;
