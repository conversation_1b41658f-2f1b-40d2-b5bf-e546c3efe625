import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Row, Col, Card, Button, Typography, Space, List } from 'antd';
import {
  SearchOutlined,
  ShareAltOutlined,
  AimOutlined,
  BgColorsOutlined,
  DesktopOutlined,
  EditOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import { SERVICES } from '../../utils/constants';

const { Title, Paragraph } = Typography;

const ServicesOverview = () => {
  const getIcon = (iconName) => {
    const icons = {
      Search: SearchOutlined,
      Share2: ShareAltOutlined,
      Target: AimOutlined,
      Palette: BgColorsOutlined,
      Monitor: DesktopOutlined,
      PenTool: EditOutlined
    };
    const IconComponent = icons[iconName];
    return IconComponent ? <IconComponent style={{ fontSize: '32px' }} /> : <SearchOutlined style={{ fontSize: '32px' }} />;
  };

  return (
    <section style={{ padding: '80px 0', background: '#fff' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          style={{ textAlign: 'center', marginBottom: '64px' }}
        >
          <Title level={2} style={{ fontSize: 'clamp(2rem, 4vw, 3rem)', marginBottom: '24px' }}>
            Our Digital Marketing Services
          </Title>
          <Paragraph style={{ fontSize: '20px', maxWidth: '800px', margin: '0 auto', color: '#666' }}>
            We offer comprehensive digital marketing solutions to help your business grow online and reach your target audience effectively.
          </Paragraph>
        </motion.div>

        {/* Services Grid */}
        <Row gutter={[32, 32]} style={{ marginBottom: '64px' }}>
          {SERVICES.map((service, index) => (
            <Col xs={24} md={12} lg={8} key={service.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card
                  hoverable
                  style={{
                    height: '100%',
                    borderRadius: '16px',
                    border: '1px solid #f0f0f0',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                    transition: 'all 0.3s ease'
                  }}
                  bodyStyle={{ padding: '32px', textAlign: 'center' }}
                  className="service-card"
                >
                  {/* Icon */}
                  <div
                    style={{
                      width: '80px',
                      height: '80px',
                      background: 'linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%)',
                      borderRadius: '20px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 24px auto',
                      color: '#0057ff',
                      transition: 'all 0.3s ease'
                    }}
                    className="service-icon"
                  >
                    {getIcon(service.icon)}
                  </div>

                  {/* Title */}
                  <Title level={4} style={{ marginBottom: '16px', color: '#000' }}>
                    {service.title}
                  </Title>

                  {/* Description */}
                  <Paragraph style={{ marginBottom: '24px', color: '#666', lineHeight: 1.6 }}>
                    {service.description}
                  </Paragraph>

                  {/* Benefits */}
                  <List
                    size="small"
                    dataSource={service.benefits.slice(0, 3)}
                    renderItem={(benefit) => (
                      <List.Item style={{ border: 'none', padding: '4px 0' }}>
                        <Space>
                          <div style={{
                            width: '6px',
                            height: '6px',
                            background: '#0057ff',
                            borderRadius: '50%'
                          }} />
                          <span style={{ fontSize: '14px', color: '#666' }}>{benefit}</span>
                        </Space>
                      </List.Item>
                    )}
                    style={{ marginBottom: '24px' }}
                  />

                  {/* Learn More Link */}
                  <Link to="/services">
                    <Button
                      type="link"
                      icon={<ArrowRightOutlined />}
                      style={{
                        color: '#0057ff',
                        fontWeight: 600,
                        padding: 0,
                        height: 'auto'
                      }}
                    >
                      Learn More
                    </Button>
                  </Link>
                </Card>
              </motion.div>
            </Col>
          ))}
        </Row>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Card
            style={{
              background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
              borderRadius: '20px',
              border: 'none',
              textAlign: 'center'
            }}
            bodyStyle={{ padding: '48px 32px' }}
          >
            <Title level={2} style={{ color: 'white', marginBottom: '16px' }}>
              Ready to Transform Your Digital Presence?
            </Title>
            <Paragraph style={{
              fontSize: '20px',
              color: 'rgba(255, 255, 255, 0.9)',
              marginBottom: '32px',
              maxWidth: '600px',
              margin: '0 auto 32px auto'
            }}>
              Let's discuss how our proven strategies can help your business achieve its digital marketing goals.
            </Paragraph>
            <Space size="large" wrap>
              <Link to="/contact">
                <Button
                  size="large"
                  style={{
                    background: 'white',
                    color: '#0057ff',
                    border: 'none',
                    borderRadius: '12px',
                    height: '48px',
                    padding: '0 32px',
                    fontWeight: 600,
                    fontSize: '16px'
                  }}
                  icon={<ArrowRightOutlined />}
                >
                  Get Free Consultation
                </Button>
              </Link>
              <Link to="/services">
                <Button
                  size="large"
                  style={{
                    background: 'transparent',
                    color: 'white',
                    border: '2px solid white',
                    borderRadius: '12px',
                    height: '48px',
                    padding: '0 32px',
                    fontWeight: 600,
                    fontSize: '16px'
                  }}
                >
                  View All Services
                </Button>
              </Link>
            </Space>
          </Card>
        </motion.div>
      </div>

      <style jsx>{`
        .service-card:hover .service-icon {
          background: linear-gradient(135deg, #0057ff 0%, #1890ff 100%) !important;
          color: white !important;
          transform: scale(1.1);
        }
      `}</style>
    </section>
  );
};

export default ServicesOverview;
