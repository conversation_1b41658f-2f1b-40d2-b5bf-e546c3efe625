import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowRight, TrendingUp, Target, Award, ExternalLink } from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import CTA from '../components/sections/CTA';
import { CASE_STUDIES } from '../utils/constants';

const CaseStudies = () => {
  useEffect(() => {
    document.title = 'Case Studies - Brandify | Success Stories & Results';
    document.querySelector('meta[name="description"]')?.setAttribute('content', 
      'Explore Brandify\'s successful digital marketing case studies. See how we\'ve helped businesses achieve remarkable growth and ROI through our proven strategies.'
    );
  }, []);

  const stats = [
    { icon: TrendingUp, value: '250%', label: 'Average Revenue Growth' },
    { icon: Target, value: '180%', label: 'Conversion Rate Improvement' },
    { icon: Award, value: '98%', label: 'Client Success Rate' }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Success Stories
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Discover how we've helped businesses like yours achieve remarkable growth through strategic digital marketing campaigns and innovative solutions.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                    <stat.icon size={32} className="text-white" />
                  </div>
                  <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-20">
            {CASE_STUDIES.map((caseStudy, index) => (
              <motion.div
                key={caseStudy.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="overflow-hidden" padding="none">
                  <div className="grid grid-cols-1 lg:grid-cols-2">
                    {/* Image */}
                    <div className="relative">
                      <img
                        src={`https://images.unsplash.com/photo-${
                          index === 0 ? '1556742049-0a6d1d18c2b8' :
                          index === 1 ? '1559757148-5c350d0d3c56' :
                          '1551434678-e076c223a692'
                        }?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                        alt={caseStudy.title}
                        className="w-full h-80 lg:h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <div className="text-sm font-medium opacity-90">{caseStudy.industry}</div>
                        <div className="text-2xl font-bold">{caseStudy.client}</div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-8 lg:p-12">
                      <h2 className="text-3xl font-bold text-gray-900 mb-6">
                        {caseStudy.title}
                      </h2>

                      {/* Challenge */}
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                          <div className="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                          Challenge
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {caseStudy.challenge}
                        </p>
                      </div>

                      {/* Solution */}
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                          Solution
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {caseStudy.solution}
                        </p>
                      </div>

                      {/* Results */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          Results
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {caseStudy.results.map((result, resultIndex) => (
                            <div
                              key={resultIndex}
                              className="bg-green-50 border border-green-200 rounded-lg p-4"
                            >
                              <div className="text-green-800 font-semibold text-lg">
                                {result}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* CTA */}
                      <div className="flex flex-col sm:flex-row gap-4">
                        <Link to="/contact">
                          <Button className="group">
                            Start Your Success Story
                            <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </Link>
                        <Button variant="outline" className="group">
                          <ExternalLink size={16} className="mr-2" />
                          View Live Project
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Overview */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              How We Deliver Results
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our proven methodology ensures consistent success across all client projects.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: '01',
                title: 'Research & Analysis',
                description: 'Deep dive into your business, market, and competition to identify opportunities.'
              },
              {
                step: '02',
                title: 'Strategy Development',
                description: 'Create a customized digital marketing strategy aligned with your business goals.'
              },
              {
                step: '03',
                title: 'Implementation',
                description: 'Execute campaigns with precision, monitoring performance at every step.'
              },
              {
                step: '04',
                title: 'Optimization',
                description: 'Continuously refine and improve based on data-driven insights.'
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center h-full" hover={true}>
                  <div className="text-5xl font-bold text-primary opacity-20 mb-4">
                    {step.step}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="text-center bg-gradient-to-br from-primary to-blue-700 text-white" padding="lg">
              <blockquote className="text-2xl md:text-3xl font-light mb-8 leading-relaxed italic">
                "Working with Brandify was a game-changer for our business. Their strategic approach and attention to detail helped us achieve results we never thought possible."
              </blockquote>
              <div className="flex items-center justify-center">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4">
                  <span className="text-primary font-bold text-xl">S</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-lg">Sarah Johnson</div>
                  <div className="text-blue-200">CEO, TechStart Solutions</div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </section>

      <CTA />
    </div>
  );
};

export default CaseStudies;
