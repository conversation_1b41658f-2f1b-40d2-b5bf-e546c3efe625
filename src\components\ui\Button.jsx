import { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

const Button = forwardRef(({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  animate = true,
  loading = false,
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-200 focus-modern disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden';

  const variants = {
    primary: 'bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 shadow-modern hover:shadow-modern-lg',
    secondary: 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700 border-modern',
    outline: 'bg-transparent text-gray-700 dark:text-gray-300 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 hover:text-blue-500 dark:hover:text-blue-400',
    ghost: 'bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800',
    gradient: 'gradient-primary text-white hover:shadow-modern-lg shadow-modern',
    gradientSecondary: 'gradient-secondary text-white hover:shadow-modern-lg shadow-modern',
    gradientAccent: 'gradient-accent text-white hover:shadow-modern-lg shadow-modern',
    destructive: 'bg-red-600 dark:bg-red-500 text-white hover:bg-red-700 dark:hover:bg-red-600 shadow-modern',
    success: 'bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 shadow-modern',
    warning: 'bg-yellow-600 dark:bg-yellow-500 text-white hover:bg-yellow-700 dark:hover:bg-yellow-600 shadow-modern',
  };

  const sizes = {
    xs: 'px-2 py-1 text-xs h-7',
    sm: 'px-3 py-1.5 text-sm h-8',
    md: 'px-6 py-2.5 text-base h-10',
    lg: 'px-8 py-3 text-lg h-12',
    xl: 'px-10 py-4 text-xl h-14'
  };

  const classes = cn(baseClasses, variants[variant], sizes[size], className);

  const ButtonComponent = (
    <button
      ref={ref}
      className={classes}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      <span className={loading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  );

  if (animate && !disabled && !loading) {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.2 }}
      >
        {ButtonComponent}
      </motion.div>
    );
  }

  return ButtonComponent;
});

Button.displayName = 'Button';

export default Button;
