import { Link } from 'react-router-dom';
import { Row, Col, Button, Space, Divider, Typography, Card, Badge } from 'antd';
import {
  LinkedinOutlined,
  InstagramOutlined,
  WhatsAppOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  RocketOutlined,
  HeartFilled,
  StarFilled,
  SendOutlined,
  GlobalOutlined,
  SafetyOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { BRAND, NAV_LINKS, SOCIAL_LINKS } from '../../utils/constants';

const { Title, Paragraph, Text } = Typography;

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  const socialIcons = {
    Linkedin: <LinkedinOutlined />,
    Instagram: <InstagramOutlined />,
    MessageCircle: <WhatsAppOutlined />
  };

  const services = [
    { name: 'SEO & SEM', icon: <GlobalOutlined /> },
    { name: 'Social Media Marketing', icon: <InstagramOutlined /> },
    { name: 'PPC Advertising', icon: <RocketOutlined /> },
    { name: 'Branding & Strategy', icon: <TrophyOutlined /> },
    { name: 'Web Development', icon: <SafetyOutlined /> },
    { name: 'Content Marketing', icon: <SendOutlined /> }
  ];

  return (
    <footer
      ref={ref}
      className="footer-gradient relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #1a202c 100%)',
        color: 'white'
      }}
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Floating particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.6, 0.2],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 6 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Grid pattern */}
        <div className="pattern-grid absolute inset-0 opacity-10" />
      </div>

      <div className="container-custom footer-section">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Main Footer Content */}
          <Row gutter={[48, 48]} className="py-16">
            {/* Enhanced Company Info */}
            <Col xs={24} md={12} lg={6}>
              <motion.div variants={itemVariants} className="space-y-6">
                {/* Logo Section */}
                <div className="flex items-center space-x-3">
                  <motion.div
                    className="relative"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div
                      className="w-12 h-12 rounded-2xl flex items-center justify-center relative overflow-hidden"
                      style={{
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                        boxShadow: '0 8px 24px rgba(0, 87, 255, 0.4)'
                      }}
                    >
                      <span className="text-white font-bold text-xl relative z-10">B</span>

                      {/* Animated shine effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
                        animate={{
                          x: ['-100%', '100%']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatDelay: 2
                        }}
                      />
                    </div>

                    {/* Floating badge */}
                    <motion.div
                      className="absolute -top-1 -right-1"
                      animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 10, 0]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity
                      }}
                    >
                      <Badge
                        count={<StarFilled style={{ color: '#faad14', fontSize: '8px' }} />}
                        style={{ backgroundColor: 'transparent' }}
                      />
                    </motion.div>
                  </motion.div>

                  <div>
                    <Title
                      level={3}
                      className="text-white m-0 gradient-text"
                      style={{
                        background: 'linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}
                    >
                      {BRAND.name}
                    </Title>
                    <Text className="text-blue-300 font-medium">{BRAND.tagline}</Text>
                  </div>
                </div>

                {/* Description */}
                <Paragraph className="text-gray-300 leading-relaxed">
                  We help ambitious businesses transform their digital presence through
                  innovative marketing strategies that deliver{' '}
                  <span className="text-blue-300 font-semibold">measurable results</span> and
                  drive sustainable growth.
                </Paragraph>

                {/* Enhanced Social Links */}
                <div className="flex space-x-4">
                  {SOCIAL_LINKS.map((social, index) => (
                    <motion.a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="social-icon w-12 h-12 rounded-xl flex items-center justify-center text-gray-300 border border-gray-600 hover:border-blue-400 transition-all duration-300"
                      style={{
                        background: 'rgba(255, 255, 255, 0.05)',
                        backdropFilter: 'blur(10px)'
                      }}
                      whileHover={{
                        scale: 1.1,
                        y: -4,
                        boxShadow: '0 8px 24px rgba(0, 87, 255, 0.3)'
                      }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      aria-label={social.name}
                    >
                      <span className="text-lg">
                        {socialIcons[social.icon]}
                      </span>
                    </motion.a>
                  ))}
                </div>

                {/* Trust Indicators */}
                <motion.div
                  className="flex items-center space-x-4 pt-4"
                  variants={itemVariants}
                >
                  <div className="flex items-center space-x-2">
                    <TrophyOutlined className="text-yellow-400" />
                    <Text className="text-gray-300 text-sm">Top Rated Agency</Text>
                  </div>
                  <div className="flex items-center space-x-2">
                    <SafetyOutlined className="text-green-400" />
                    <Text className="text-gray-300 text-sm">Trusted by 500+</Text>
                  </div>
                </motion.div>
              </motion.div>
            </Col>

            {/* Enhanced Quick Links */}
            <Col xs={24} md={12} lg={6}>
              <motion.div variants={itemVariants} className="space-y-6">
                <Title level={4} className="text-white mb-6 flex items-center gap-2">
                  <RocketOutlined className="text-blue-400" />
                  Quick Links
                </Title>

                <div className="space-y-3">
                  {NAV_LINKS.map((link, index) => (
                    <motion.div
                      key={link.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        to={link.path}
                        className="footer-link block text-gray-300 hover:text-blue-300 transition-all duration-300 text-base font-medium py-2 px-3 rounded-lg hover:bg-white/5"
                      >
                        <span className="flex items-center gap-2">
                          <span className="w-1 h-1 bg-blue-400 rounded-full opacity-60"></span>
                          {link.name}
                        </span>
                      </Link>
                    </motion.div>
                  ))}
                </div>

                {/* CTA Card */}
                <motion.div
                  variants={itemVariants}
                  className="mt-8"
                >
                  <Card
                    className="glass border-0"
                    style={{
                      background: 'rgba(0, 87, 255, 0.1)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(0, 87, 255, 0.2)',
                      borderRadius: '16px'
                    }}
                  >
                    <div className="text-center">
                      <RocketOutlined className="text-blue-400 text-2xl mb-2" />
                      <Text className="text-white font-semibold block mb-2">
                        Ready to grow?
                      </Text>
                      <Link to="/contact">
                        <Button
                          type="primary"
                          size="small"
                          className="btn-modern"
                          style={{
                            background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                            border: 'none',
                            borderRadius: '8px'
                          }}
                        >
                          Get Started
                        </Button>
                      </Link>
                    </div>
                  </Card>
                </motion.div>
              </motion.div>
            </Col>

            {/* Enhanced Services */}
            <Col xs={24} md={12} lg={6}>
              <motion.div variants={itemVariants} className="space-y-6">
                <Title level={4} className="text-white mb-6 flex items-center gap-2">
                  <TrophyOutlined className="text-yellow-400" />
                  Our Services
                </Title>

                <div className="space-y-3">
                  {services.map((service, index) => (
                    <motion.div
                      key={service.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        to="/services"
                        className="footer-link group flex items-center gap-3 text-gray-300 hover:text-blue-300 transition-all duration-300 text-base py-2 px-3 rounded-lg hover:bg-white/5"
                      >
                        <span className="text-blue-400 group-hover:text-blue-300 transition-colors duration-300">
                          {service.icon}
                        </span>
                        <span className="font-medium">{service.name}</span>
                      </Link>
                    </motion.div>
                  ))}
                </div>

                {/* Service Stats */}
                <motion.div
                  variants={itemVariants}
                  className="mt-8 p-4 rounded-xl"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                  }}
                >
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-blue-400 font-bold text-lg">500+</div>
                      <div className="text-gray-400 text-xs">Projects</div>
                    </div>
                    <div>
                      <div className="text-green-400 font-bold text-lg">98%</div>
                      <div className="text-gray-400 text-xs">Success</div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </Col>

            {/* Enhanced Contact Info */}
            <Col xs={24} md={12} lg={6}>
              <motion.div variants={itemVariants} className="space-y-6">
                <Title level={4} className="text-white mb-6 flex items-center gap-2">
                  <MailOutlined className="text-green-400" />
                  Get In Touch
                </Title>

                <div className="space-y-4">
                  {/* Phone */}
                  <motion.a
                    href={`tel:${BRAND.phone}`}
                    className="footer-link group flex items-center gap-4 text-gray-300 hover:text-blue-300 transition-all duration-300 p-3 rounded-xl hover:bg-white/5"
                    whileHover={{ x: 4 }}
                  >
                    <div className="w-10 h-10 rounded-xl bg-blue-500/20 flex items-center justify-center group-hover:bg-blue-500/30 transition-colors duration-300">
                      <PhoneOutlined className="text-blue-400" />
                    </div>
                    <div>
                      <div className="font-medium">{BRAND.phone}</div>
                      <div className="text-gray-400 text-sm">Call us anytime</div>
                    </div>
                  </motion.a>

                  {/* Email */}
                  <motion.a
                    href={`mailto:${BRAND.email}`}
                    className="footer-link group flex items-center gap-4 text-gray-300 hover:text-blue-300 transition-all duration-300 p-3 rounded-xl hover:bg-white/5"
                    whileHover={{ x: 4 }}
                  >
                    <div className="w-10 h-10 rounded-xl bg-green-500/20 flex items-center justify-center group-hover:bg-green-500/30 transition-colors duration-300">
                      <MailOutlined className="text-green-400" />
                    </div>
                    <div>
                      <div className="font-medium">{BRAND.email}</div>
                      <div className="text-gray-400 text-sm">Send us a message</div>
                    </div>
                  </motion.a>

                  {/* Address */}
                  <motion.div
                    className="group flex items-start gap-4 text-gray-300 p-3 rounded-xl"
                    whileHover={{ x: 4 }}
                  >
                    <div className="w-10 h-10 rounded-xl bg-purple-500/20 flex items-center justify-center group-hover:bg-purple-500/30 transition-colors duration-300">
                      <EnvironmentOutlined className="text-purple-400" />
                    </div>
                    <div>
                      <div className="font-medium">{BRAND.address}</div>
                      <div className="text-gray-400 text-sm">Visit our office</div>
                    </div>
                  </motion.div>
                </div>

                {/* Business Hours */}
                <motion.div
                  variants={itemVariants}
                  className="mt-6 p-4 rounded-xl"
                  style={{
                    background: 'rgba(0, 87, 255, 0.1)',
                    border: '1px solid rgba(0, 87, 255, 0.2)'
                  }}
                >
                  <Title level={5} className="text-white mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                    Business Hours
                  </Title>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between text-gray-300">
                      <span>Mon - Fri:</span>
                      <span className="text-green-400 font-medium">9:00 AM - 6:00 PM</span>
                    </div>
                    <div className="flex justify-between text-gray-300">
                      <span>Saturday:</span>
                      <span className="text-yellow-400 font-medium">10:00 AM - 4:00 PM</span>
                    </div>
                    <div className="flex justify-between text-gray-300">
                      <span>Sunday:</span>
                      <span className="text-red-400 font-medium">Closed</span>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </Col>
          </Row>

          {/* Enhanced Bottom Bar */}
          <motion.div
            variants={itemVariants}
            className="mt-16 pt-8"
            style={{
              borderTop: '1px solid rgba(255, 255, 255, 0.1)'
            }}
          >
            <Row gutter={[24, 24]} align="middle">
              <Col xs={24} md={12}>
                <motion.div
                  className="flex items-center gap-2 text-gray-300"
                  whileHover={{ scale: 1.02 }}
                >
                  <Text className="text-gray-400">
                    © {currentYear} {BRAND.name}. Made with
                  </Text>
                  <motion.span
                    animate={{
                      scale: [1, 1.2, 1],
                      color: ['#ef4444', '#f59e0b', '#ef4444']
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity
                    }}
                  >
                    <HeartFilled className="text-red-400" />
                  </motion.span>
                  <Text className="text-gray-400">
                    for ambitious businesses.
                  </Text>
                </motion.div>
              </Col>

              <Col xs={24} md={12}>
                <div className="flex flex-wrap gap-6 justify-start md:justify-end">
                  <Link
                    to="/privacy"
                    className="footer-link text-gray-400 hover:text-blue-300 text-sm transition-all duration-300 font-medium"
                  >
                    Privacy Policy
                  </Link>
                  <Link
                    to="/terms"
                    className="footer-link text-gray-400 hover:text-blue-300 text-sm transition-all duration-300 font-medium"
                  >
                    Terms of Service
                  </Link>
                  <Link
                    to="/sitemap"
                    className="footer-link text-gray-400 hover:text-blue-300 text-sm transition-all duration-300 font-medium"
                  >
                    Sitemap
                  </Link>
                </div>
              </Col>
            </Row>

            {/* Additional Footer Elements */}
            <motion.div
              className="mt-8 pt-6 text-center"
              style={{
                borderTop: '1px solid rgba(255, 255, 255, 0.05)'
              }}
              variants={itemVariants}
            >
              <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <SafetyOutlined className="text-green-400" />
                  <span>SSL Secured</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrophyOutlined className="text-yellow-400" />
                  <span>Award Winning</span>
                </div>
                <div className="flex items-center gap-2">
                  <GlobalOutlined className="text-blue-400" />
                  <span>Global Reach</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                  <span>24/7 Support</span>
                </div>
              </div>

              <motion.div
                className="mt-4 text-xs text-gray-500"
                animate={{
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity
                }}
              >
                Transforming businesses through digital excellence since 2019
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
