import { useEffect } from 'react';
import ModernHero from '../components/sections/ModernHero';
import ServicesOverview from '../components/sections/ServicesOverview';
import AboutPreview from '../components/sections/AboutPreview';
import Testimonials from '../components/sections/Testimonials';
import ClientLogos from '../components/sections/ClientLogos';
import CTA from '../components/sections/CTA';

const Home = () => {
  useEffect(() => {
    document.title = 'Brandify - Empowering Brands Digitally | Digital Marketing Agency';
    document.querySelector('meta[name="description"]')?.setAttribute('content', 
      'Brandify is a leading digital marketing agency helping businesses grow online through SEO, social media marketing, PPC advertising, and web development services.'
    );
  }, []);

  return (
    <div>
      <ModernHero />
      <ServicesOverview />
      <AboutPreview />
      <Testimonials />
      <ClientLogos />
      <CTA />
    </div>
  );
};

export default Home;
