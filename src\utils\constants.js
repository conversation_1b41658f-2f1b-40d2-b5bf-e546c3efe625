// Brand Constants
export const BRAND = {
  name: 'Brandify',
  tagline: 'Empowering Brands Digitally',
  phone: '+****************',
  email: '<EMAIL>',
  address: '123 Digital Avenue, Tech City, TC 12345',
  whatsapp: '+****************',
  businessHours: {
    weekdays: 'Monday - Friday: 9:00 AM - 6:00 PM',
    weekend: 'Saturday: 10:00 AM - 4:00 PM',
    closed: 'Sunday: Closed'
  }
};

// Navigation Links
export const NAV_LINKS = [
  { name: 'Home', path: '/' },
  { name: 'About', path: '/about' },
  { name: 'Services', path: '/services' },
  { name: 'Case Studies', path: '/case-studies' },
  { name: 'Blog', path: '/blog' },
  { name: 'Contact', path: '/contact' }
];

// Services Data
export const SERVICES = [
  {
    id: 1,
    title: 'SEO & SEM',
    description: 'Boost your online visibility with our comprehensive search engine optimization and marketing strategies.',
    benefits: ['Increased organic traffic', 'Higher search rankings', 'Better ROI on ad spend', 'Local SEO optimization'],
    icon: 'Search'
  },
  {
    id: 2,
    title: 'Social Media Marketing',
    description: 'Engage your audience and build brand loyalty through strategic social media campaigns.',
    benefits: ['Enhanced brand awareness', 'Increased engagement', 'Community building', 'Lead generation'],
    icon: 'Share2'
  },
  {
    id: 3,
    title: 'Pay-Per-Click Advertising',
    description: 'Drive immediate results with targeted Google Ads and Meta advertising campaigns.',
    benefits: ['Instant traffic generation', 'Precise audience targeting', 'Measurable results', 'Cost-effective campaigns'],
    icon: 'Target'
  },
  {
    id: 4,
    title: 'Branding & Strategy',
    description: 'Develop a compelling brand identity that resonates with your target audience.',
    benefits: ['Strong brand identity', 'Market positioning', 'Brand guidelines', 'Competitive advantage'],
    icon: 'Palette'
  },
  {
    id: 5,
    title: 'Web Design & Development',
    description: 'Create stunning, responsive websites that convert visitors into customers.',
    benefits: ['Mobile-responsive design', 'Fast loading speeds', 'SEO-optimized structure', 'User-friendly interface'],
    icon: 'Monitor'
  },
  {
    id: 6,
    title: 'Content Marketing',
    description: 'Tell your brand story through compelling content that drives engagement and conversions.',
    benefits: ['Quality content creation', 'Brand storytelling', 'Audience engagement', 'Thought leadership'],
    icon: 'PenTool'
  }
];

// Testimonials
export const TESTIMONIALS = [
  {
    id: 1,
    name: 'Sarah Johnson',
    company: 'TechStart Solutions',
    role: 'CEO',
    content: 'Brandify transformed our digital presence completely. Our website traffic increased by 300% and lead generation improved dramatically within just 3 months.',
    rating: 5
  },
  {
    id: 2,
    name: 'Michael Chen',
    company: 'GreenLife Organics',
    role: 'Marketing Director',
    content: 'The team at Brandify understands our industry perfectly. Their social media campaigns helped us reach a younger demographic and boost sales by 150%.',
    rating: 5
  },
  {
    id: 3,
    name: 'Emily Rodriguez',
    company: 'Urban Fitness Studio',
    role: 'Owner',
    content: 'Working with Brandify was the best decision for our business. Their comprehensive digital strategy helped us expand to three new locations.',
    rating: 5
  }
];

// Case Studies
export const CASE_STUDIES = [
  {
    id: 1,
    title: 'E-commerce Revenue Growth',
    client: 'Fashion Forward Boutique',
    industry: 'Retail & Fashion',
    challenge: 'Low online sales and poor website conversion rates were limiting business growth despite having quality products.',
    solution: 'Implemented comprehensive SEO strategy, redesigned website for better UX, and launched targeted Google Ads campaigns.',
    results: ['250% increase in online revenue', '180% improvement in conversion rate', '400% growth in organic traffic', '65% reduction in cost per acquisition'],
    image: '/api/placeholder/600/400'
  },
  {
    id: 2,
    title: 'Local Business Digital Transformation',
    client: 'Metro Dental Care',
    industry: 'Healthcare',
    challenge: 'Traditional dental practice struggling to attract new patients and compete with larger chains in the area.',
    solution: 'Developed local SEO strategy, created engaging social media presence, and implemented online appointment booking system.',
    results: ['300% increase in new patient inquiries', '85% improvement in local search rankings', '200% growth in social media followers', '40% increase in appointment bookings'],
    image: '/api/placeholder/600/400'
  },
  {
    id: 3,
    title: 'B2B Lead Generation Success',
    client: 'CloudTech Solutions',
    industry: 'Technology',
    challenge: 'Software company needed to generate qualified leads for their enterprise solutions in a competitive market.',
    solution: 'Created thought leadership content strategy, optimized LinkedIn campaigns, and developed lead nurturing workflows.',
    results: ['500% increase in qualified leads', '120% improvement in lead quality score', '75% reduction in sales cycle length', '350% ROI on marketing spend'],
    image: '/api/placeholder/600/400'
  }
];

// Blog Posts
export const BLOG_POSTS = [
  {
    id: 1,
    title: '10 Digital Marketing Trends to Watch in 2024',
    excerpt: 'Discover the latest digital marketing trends that will shape your strategy this year, from AI-powered personalization to voice search optimization.',
    publishDate: '2024-01-15',
    readTime: '5 min read',
    category: 'Trends',
    image: '/api/placeholder/400/250'
  },
  {
    id: 2,
    title: 'How to Measure ROI in Social Media Marketing',
    excerpt: 'Learn practical methods to track and measure the return on investment of your social media campaigns with actionable metrics and tools.',
    publishDate: '2024-01-10',
    readTime: '7 min read',
    category: 'Analytics',
    image: '/api/placeholder/400/250'
  },
  {
    id: 3,
    title: 'The Complete Guide to Local SEO for Small Businesses',
    excerpt: 'Master local SEO strategies that help small businesses dominate local search results and attract more customers from their community.',
    publishDate: '2024-01-05',
    readTime: '10 min read',
    category: 'SEO',
    image: '/api/placeholder/400/250'
  },
  {
    id: 4,
    title: 'Creating Compelling Content That Converts',
    excerpt: 'Discover the secrets to creating content that not only engages your audience but also drives them to take action and convert.',
    publishDate: '2023-12-28',
    readTime: '6 min read',
    category: 'Content Marketing',
    image: '/api/placeholder/400/250'
  },
  {
    id: 5,
    title: 'Google Ads vs Facebook Ads: Which is Right for Your Business?',
    excerpt: 'Compare the strengths and weaknesses of Google Ads and Facebook Ads to determine the best advertising platform for your business goals.',
    publishDate: '2023-12-20',
    readTime: '8 min read',
    category: 'Paid Advertising',
    image: '/api/placeholder/400/250'
  },
  {
    id: 6,
    title: 'Building a Strong Brand Identity in the Digital Age',
    excerpt: 'Learn how to create a memorable brand identity that stands out in the digital landscape and resonates with your target audience.',
    publishDate: '2023-12-15',
    readTime: '9 min read',
    category: 'Branding',
    image: '/api/placeholder/400/250'
  }
];

// Client Logos (placeholder company names)
export const CLIENT_LOGOS = [
  { name: 'TechStart Solutions', logo: '/api/placeholder/150/80' },
  { name: 'GreenLife Organics', logo: '/api/placeholder/150/80' },
  { name: 'Urban Fitness Studio', logo: '/api/placeholder/150/80' },
  { name: 'Fashion Forward Boutique', logo: '/api/placeholder/150/80' },
  { name: 'Metro Dental Care', logo: '/api/placeholder/150/80' },
  { name: 'CloudTech Solutions', logo: '/api/placeholder/150/80' },
  { name: 'Sunrise Bakery', logo: '/api/placeholder/150/80' },
  { name: 'Elite Consulting Group', logo: '/api/placeholder/150/80' }
];

// Social Media Links
export const SOCIAL_LINKS = [
  { name: 'LinkedIn', url: 'https://linkedin.com/company/brandify', icon: 'Linkedin' },
  { name: 'Instagram', url: 'https://instagram.com/brandify', icon: 'Instagram' },
  { name: 'WhatsApp', url: 'https://wa.me/15551234567', icon: 'MessageCircle' }
];

// Service Interest Options for Contact Form
export const SERVICE_OPTIONS = [
  'SEO & SEM',
  'Social Media Marketing',
  'Pay-Per-Click Advertising',
  'Branding & Strategy',
  'Web Design & Development',
  'Content Marketing',
  'Full Digital Marketing Package',
  'Consultation Only'
];
