import { Link } from 'react-router-dom';
import { Row, Col, Button, Typography, Space, Statistic, Card, Badge, Avatar } from 'antd';
import {
  ArrowRightOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  TeamOutlined,
  CalendarOutlined,
  RocketOutlined,
  StarFilled,
  CheckCircleOutlined,
  <PERSON>boltOutlined,
  FireOutlined
} from '@ant-design/icons';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import CountUp from 'react-countup';
import Tilt from 'react-parallax-tilt';
import { BRAND } from '../../utils/constants';

const { Title, Paragraph } = Typography;

const Hero = () => {
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, -100]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0.3]);

  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  return (
    <section
      ref={ref}
      className="relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f0f9ff 100%)',
        padding: '140px 0 100px 0',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center'
      }}
    >
      {/* Enhanced Background Pattern */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{ opacity }}
      >
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute"
          style={{
            top: '10%',
            left: '10%',
            width: '400px',
            height: '400px',
            background: 'radial-gradient(circle, rgba(0,87,255,0.15) 0%, rgba(0,87,255,0.05) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(60px)',
            y: y1
          }}
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <motion.div
          className="absolute"
          style={{
            top: '20%',
            right: '10%',
            width: '300px',
            height: '300px',
            background: 'radial-gradient(circle, rgba(24,144,255,0.12) 0%, rgba(24,144,255,0.04) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(80px)',
            y: y2
          }}
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <motion.div
          className="absolute"
          style={{
            bottom: '10%',
            left: '20%',
            width: '250px',
            height: '250px',
            background: 'radial-gradient(circle, rgba(64,169,255,0.1) 0%, rgba(64,169,255,0.03) 50%, transparent 100%)',
            borderRadius: '50%',
            filter: 'blur(70px)',
          }}
          animate={{
            scale: [1, 1.3, 1],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400 rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 50 - 25, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Grid pattern overlay */}
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0,87,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0,87,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'float 20s ease-in-out infinite'
          }}
        />
      </motion.div>

      <div className="container-custom relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <Row gutter={[64, 64]} align="middle">
            {/* Enhanced Content */}
            <Col xs={24} lg={12}>
              <motion.div variants={itemVariants}>
                {/* Enhanced Brand Badge */}
                <motion.div
                  variants={itemVariants}
                  className="mb-8"
                >
                  <Badge.Ribbon
                    text={
                      <span className="flex items-center gap-1">
                        <StarFilled className="text-yellow-400" />
                        <span className="font-semibold">Top Rated Agency</span>
                      </span>
                    }
                    color="#0057ff"
                    placement="start"
                  >
                    <Card
                      className="inline-block glass border-0"
                      style={{
                        background: 'rgba(255, 255, 255, 0.9)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '16px',
                        padding: '8px 16px'
                      }}
                    >
                      <Space size="middle" align="center">
                        <motion.div
                          className="relative"
                          whileHover={{
                            rotate: [0, -10, 10, 0],
                            scale: 1.1
                          }}
                          transition={{ duration: 0.5 }}
                        >
                          <div
                            className="w-14 h-14 rounded-2xl flex items-center justify-center relative overflow-hidden"
                            style={{
                              background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 50%, #40a9ff 100%)',
                              boxShadow: '0 12px 32px rgba(0, 87, 255, 0.4)'
                            }}
                          >
                            <span className="text-white font-bold text-2xl relative z-10">B</span>

                            {/* Animated shine effect */}
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
                              animate={{
                                x: ['-100%', '100%']
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                repeatDelay: 3
                              }}
                            />
                          </div>

                          {/* Floating sparkles */}
                          {[...Array(3)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-1 h-1 bg-yellow-400 rounded-full"
                              style={{
                                top: `${20 + i * 15}%`,
                                right: `${10 + i * 10}%`,
                              }}
                              animate={{
                                opacity: [0, 1, 0],
                                scale: [0, 1, 0],
                                y: [0, -10, 0],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                delay: i * 0.5,
                              }}
                            />
                          ))}
                        </motion.div>

                        <div>
                          <Title
                            level={2}
                            className="gradient-text m-0"
                            style={{
                              fontSize: '28px',
                              background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                              WebkitBackgroundClip: 'text',
                              WebkitTextFillColor: 'transparent'
                            }}
                          >
                            {BRAND.name}
                          </Title>
                          <motion.div
                            className="text-blue-600 font-semibold text-lg"
                            animate={{
                              opacity: [0.7, 1, 0.7]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity
                            }}
                          >
                            {BRAND.tagline}
                          </motion.div>
                        </div>
                      </Space>
                    </Card>
                  </Badge.Ribbon>
                </motion.div>

                {/* Enhanced Main Heading */}
                <motion.div variants={itemVariants}>
                  <Title
                    level={1}
                    className="text-hero mb-6"
                    style={{
                      lineHeight: 1.1,
                      marginBottom: '32px'
                    }}
                  >
                    <motion.span
                      className="block"
                      style={{
                        background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}
                      animate={{
                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                      }}
                      transition={{
                        duration: 5,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    >
                      Transform Your Business
                    </motion.span>

                    <motion.span
                      className="block"
                      style={{
                        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 50%, #40a9ff 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundSize: '200% 100%'
                      }}
                      animate={{
                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    >
                      with Digital Excellence
                    </motion.span>

                    {/* Animated underline */}
                    <motion.div
                      className="h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-4"
                      initial={{ width: 0 }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 1.5, delay: 1 }}
                    />
                  </Title>
                </motion.div>

                {/* Enhanced Description */}
                <motion.div variants={itemVariants}>
                  <Paragraph
                    className="text-xl leading-relaxed mb-8 text-gray-600"
                    style={{
                      fontSize: '22px',
                      lineHeight: 1.7,
                      marginBottom: '48px',
                      maxWidth: '600px'
                    }}
                  >
                    We help ambitious businesses like yours{' '}
                    <motion.span
                      className="font-semibold text-blue-600"
                      whileHover={{ scale: 1.05 }}
                    >
                      skyrocket their online presence
                    </motion.span>
                    , generate high-quality leads, and{' '}
                    <motion.span
                      className="font-semibold text-green-600"
                      whileHover={{ scale: 1.05 }}
                    >
                      boost revenue by 300%+
                    </motion.span>
                    {' '}through data-driven digital marketing strategies that deliver{' '}
                    <motion.span
                      className="font-semibold text-purple-600"
                      whileHover={{ scale: 1.05 }}
                    >
                      measurable results
                    </motion.span>
                    .
                  </Paragraph>

                  {/* Key benefits */}
                  <motion.div
                    className="flex flex-wrap gap-4 mb-8"
                    variants={containerVariants}
                  >
                    {[
                      { icon: <CheckCircleOutlined />, text: "Guaranteed ROI", color: "#10b981" },
                      { icon: <ThunderboltOutlined />, text: "Fast Results", color: "#f59e0b" },
                      { icon: <FireOutlined />, text: "Expert Team", color: "#ef4444" }
                    ].map((benefit, index) => (
                      <motion.div
                        key={index}
                        variants={itemVariants}
                        className="flex items-center gap-2 px-4 py-2 bg-white rounded-full shadow-md border border-gray-100"
                        whileHover={{
                          scale: 1.05,
                          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        <span style={{ color: benefit.color, fontSize: '16px' }}>
                          {benefit.icon}
                        </span>
                        <span className="font-medium text-gray-700">{benefit.text}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>

                {/* Enhanced CTA Buttons */}
                <motion.div
                  variants={itemVariants}
                  className="mb-12"
                >
                  <Space size="large" wrap className="w-full">
                    {/* Primary CTA */}
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link to="/contact">
                        <Button
                          type="primary"
                          size="large"
                          icon={<RocketOutlined />}
                          className="btn-gradient hover-glow"
                          style={{
                            height: '64px',
                            padding: '0 40px',
                            fontSize: '20px',
                            fontWeight: 700,
                            borderRadius: '16px',
                            background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 100%)',
                            border: 'none',
                            boxShadow: '0 12px 32px rgba(0, 87, 255, 0.4)',
                            position: 'relative',
                            overflow: 'hidden'
                          }}
                        >
                          <span className="relative z-10 flex items-center gap-2">
                            Get Free Audit
                            <motion.span
                              animate={{ x: [0, 5, 0] }}
                              transition={{ duration: 1.5, repeat: Infinity }}
                            >
                              →
                            </motion.span>
                          </span>

                          {/* Animated background */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"
                            initial={{ x: '-100%' }}
                            whileHover={{ x: '0%' }}
                            transition={{ duration: 0.3 }}
                          />
                        </Button>
                      </Link>
                    </motion.div>

                    {/* Secondary CTA */}
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        size="large"
                        icon={<PlayCircleOutlined />}
                        className="glass hover-lift"
                        style={{
                          height: '64px',
                          padding: '0 40px',
                          fontSize: '20px',
                          fontWeight: 600,
                          borderRadius: '16px',
                          borderColor: '#0057ff',
                          color: '#0057ff',
                          background: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        <span className="flex items-center gap-2">
                          Watch Demo
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            ▶
                          </motion.div>
                        </span>
                      </Button>
                    </motion.div>
                  </Space>

                  {/* Trust indicators */}
                  <motion.div
                    className="mt-6 flex items-center gap-6 text-sm text-gray-500"
                    variants={itemVariants}
                  >
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <span>No setup fees</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <span>Cancel anytime</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <span>Results in 30 days</span>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Enhanced Stats */}
                <motion.div variants={itemVariants}>
                  <div className="pt-8 border-t border-gray-200">
                    <Row gutter={[32, 24]}>
                      {[
                        {
                          title: "Happy Clients",
                          value: 500,
                          suffix: "+",
                          icon: <TeamOutlined />,
                          color: "#0057ff",
                          description: "Satisfied customers worldwide"
                        },
                        {
                          title: "Success Rate",
                          value: 98,
                          suffix: "%",
                          icon: <TrophyOutlined />,
                          color: "#10b981",
                          description: "Project completion rate"
                        },
                        {
                          title: "Years Experience",
                          value: 5,
                          suffix: "+",
                          icon: <CalendarOutlined />,
                          color: "#f59e0b",
                          description: "In digital marketing"
                        }
                      ].map((stat, index) => (
                        <Col xs={8} key={index}>
                          <motion.div
                            className="text-center"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.2 }}
                          >
                            <motion.div
                              className="inline-flex items-center justify-center w-12 h-12 rounded-xl mb-3"
                              style={{
                                background: `${stat.color}15`,
                                color: stat.color
                              }}
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.5 }}
                            >
                              {stat.icon}
                            </motion.div>

                            <div className="text-3xl font-bold mb-1" style={{ color: stat.color }}>
                              {inView && (
                                <CountUp
                                  end={stat.value}
                                  duration={2.5}
                                  delay={index * 0.2}
                                />
                              )}
                              {stat.suffix}
                            </div>

                            <div className="text-gray-900 font-semibold text-lg mb-1">
                              {stat.title}
                            </div>

                            <div className="text-gray-500 text-sm">
                              {stat.description}
                            </div>
                          </motion.div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </motion.div>
              </motion.div>
            </Col>

            {/* Enhanced Hero Image */}
            <Col xs={24} lg={12}>
              <motion.div
                variants={itemVariants}
                className="relative"
              >
                <Tilt
                  tiltMaxAngleX={5}
                  tiltMaxAngleY={5}
                  perspective={1000}
                  scale={1.02}
                  transitionSpeed={1000}
                  gyroscope={true}
                >
                  <div className="relative z-10">
                    <motion.div
                      className="relative overflow-hidden rounded-3xl"
                      style={{
                        boxShadow: '0 25px 60px rgba(0, 0, 0, 0.15)'
                      }}
                      whileHover={{
                        boxShadow: '0 35px 80px rgba(0, 87, 255, 0.2)'
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                        alt="Digital Marketing Dashboard"
                        className="w-full h-auto"
                        style={{
                          borderRadius: '24px',
                          filter: 'brightness(1.1) contrast(1.1)'
                        }}
                      />

                      {/* Overlay gradient */}
                      <div
                        className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 to-purple-500/10 rounded-3xl"
                        style={{ mixBlendMode: 'overlay' }}
                      />

                      {/* Animated border */}
                      <motion.div
                        className="absolute inset-0 rounded-3xl"
                        style={{
                          background: 'linear-gradient(45deg, #0057ff, #1890ff, #40a9ff, #0057ff)',
                          backgroundSize: '300% 300%',
                          padding: '2px'
                        }}
                        animate={{
                          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      >
                        <div className="w-full h-full bg-white rounded-3xl" />
                      </motion.div>
                    </motion.div>
                  </div>
                </Tilt>

                {/* Enhanced Floating Cards */}
                <motion.div
                  className="absolute -top-6 -left-6 hidden lg:block"
                  initial={{ opacity: 0, y: 20, rotate: -5 }}
                  animate={{ opacity: 1, y: 0, rotate: 0 }}
                  transition={{ duration: 0.8, delay: 1.2 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <Card
                    className="glass border-0"
                    style={{
                      borderRadius: '16px',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      boxShadow: '0 12px 32px rgba(0, 0, 0, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <Space align="center">
                      <motion.div
                        className="w-3 h-3 bg-green-500 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <span className="font-bold text-green-600">Traffic +250%</span>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        📈
                      </motion.div>
                    </Space>
                  </Card>
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -right-6 hidden lg:block"
                  initial={{ opacity: 0, y: 20, rotate: 5 }}
                  animate={{ opacity: 1, y: 0, rotate: 0 }}
                  transition={{ duration: 0.8, delay: 1.4 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: -5,
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <Card
                    className="glass border-0"
                    style={{
                      borderRadius: '16px',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      boxShadow: '0 12px 32px rgba(0, 0, 0, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <Space align="center">
                      <motion.div
                        className="w-3 h-3 bg-blue-500 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                      />
                      <span className="font-bold text-blue-600">ROI +180%</span>
                      <motion.div
                        animate={{ rotate: -360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        💰
                      </motion.div>
                    </Space>
                  </Card>
                </motion.div>

                {/* Additional floating elements */}
                <motion.div
                  className="absolute top-1/2 -right-12 hidden xl:block"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 1.6 }}
                >
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-xl"
                    animate={{
                      y: [0, -10, 0],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    whileHover={{ scale: 1.2 }}
                  >
                    🚀
                  </motion.div>
                </motion.div>

                <motion.div
                  className="absolute top-1/4 -left-8 hidden xl:block"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 1.8 }}
                >
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-white font-bold"
                    animate={{
                      y: [0, 10, 0],
                      rotate: [0, -5, 5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                    whileHover={{ scale: 1.2 }}
                  >
                    ⭐
                  </motion.div>
                </motion.div>
              </motion.div>
            </Col>
          </Row>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
