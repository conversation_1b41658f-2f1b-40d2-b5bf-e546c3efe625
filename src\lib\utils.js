import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export const themes = {
  light: {
    background: 'bg-white',
    foreground: 'text-gray-900',
    card: 'bg-white',
    cardForeground: 'text-gray-900',
    popover: 'bg-white',
    popoverForeground: 'text-gray-900',
    primary: 'bg-blue-600',
    primaryForeground: 'text-white',
    secondary: 'bg-gray-100',
    secondaryForeground: 'text-gray-900',
    muted: 'bg-gray-100',
    mutedForeground: 'text-gray-500',
    accent: 'bg-gray-100',
    accentForeground: 'text-gray-900',
    destructive: 'bg-red-500',
    destructiveForeground: 'text-white',
    border: 'border-gray-200',
    input: 'border-gray-200',
    ring: 'ring-blue-600',
  },
  dark: {
    background: 'bg-gray-950',
    foreground: 'text-gray-50',
    card: 'bg-gray-950',
    cardForeground: 'text-gray-50',
    popover: 'bg-gray-950',
    popoverForeground: 'text-gray-50',
    primary: 'bg-blue-500',
    primaryForeground: 'text-gray-50',
    secondary: 'bg-gray-800',
    secondaryForeground: 'text-gray-50',
    muted: 'bg-gray-800',
    mutedForeground: 'text-gray-400',
    accent: 'bg-gray-800',
    accentForeground: 'text-gray-50',
    destructive: 'bg-red-900',
    destructiveForeground: 'text-gray-50',
    border: 'border-gray-800',
    input: 'border-gray-800',
    ring: 'ring-blue-500',
  },
};

export const gradients = {
  primary: 'bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600',
  secondary: 'bg-gradient-to-r from-purple-600 via-pink-600 to-red-600',
  accent: 'bg-gradient-to-r from-green-600 via-teal-600 to-cyan-600',
  warm: 'bg-gradient-to-r from-orange-600 via-red-600 to-pink-600',
  cool: 'bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600',
};

export const animations = {
  fadeIn: 'animate-in fade-in duration-500',
  slideUp: 'animate-in slide-in-from-bottom-4 duration-500',
  slideDown: 'animate-in slide-in-from-top-4 duration-500',
  slideLeft: 'animate-in slide-in-from-right-4 duration-500',
  slideRight: 'animate-in slide-in-from-left-4 duration-500',
  scaleIn: 'animate-in zoom-in-95 duration-300',
  scaleOut: 'animate-out zoom-out-95 duration-200',
};
