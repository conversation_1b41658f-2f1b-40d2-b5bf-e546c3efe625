import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowRight, Phone, Mail } from 'lucide-react';
import Button from '../ui/Button';
import { BRAND } from '../../utils/constants';

const CTA = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-primary to-blue-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full mix-blend-overlay filter blur-xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full mix-blend-overlay filter blur-xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center text-white">
          {/* Main CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Ready to Grow?
              <span className="block text-blue-200">Let's Talk.</span>
            </h2>
            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Take the first step towards digital success. Get a free consultation and discover how we can help your business thrive online.
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-6 justify-center mb-12"
          >
            <Link to="/contact">
              <Button 
                variant="secondary" 
                size="xl" 
                className="group min-w-[200px]"
              >
                Get Free Audit
                <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            <a href={`tel:${BRAND.phone}`}>
              <Button 
                variant="ghost" 
                size="xl" 
                className="text-white border-white hover:bg-white hover:text-primary min-w-[200px]"
              >
                <Phone size={20} className="mr-2" />
                Call Now
              </Button>
            </a>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto"
          >
            <div className="flex items-center justify-center space-x-3">
              <Phone size={24} className="text-blue-200" />
              <div>
                <div className="text-sm text-blue-200">Call us directly</div>
                <a 
                  href={`tel:${BRAND.phone}`}
                  className="text-lg font-semibold hover:text-blue-200 transition-colors"
                >
                  {BRAND.phone}
                </a>
              </div>
            </div>
            <div className="flex items-center justify-center space-x-3">
              <Mail size={24} className="text-blue-200" />
              <div>
                <div className="text-sm text-blue-200">Email us</div>
                <a 
                  href={`mailto:${BRAND.email}`}
                  className="text-lg font-semibold hover:text-blue-200 transition-colors"
                >
                  {BRAND.email}
                </a>
              </div>
            </div>
          </motion.div>

          {/* Guarantee */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-12 pt-8 border-t border-blue-400 border-opacity-30"
          >
            <p className="text-blue-100 text-lg">
              🚀 <strong>Free consultation</strong> • 📈 <strong>No long-term contracts</strong> • ⚡ <strong>Quick response time</strong>
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
