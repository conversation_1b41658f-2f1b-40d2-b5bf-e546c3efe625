import { Link } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { Row, Col, Button, Typography, Space, Card, Avatar, Badge, Statistic } from 'antd';
import {
  ArrowRightOutlined,
  PhoneOutlined,
  MailOutlined,
  RocketOutlined,
  StarFilled,
  CheckCircleOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  CrownOutlined,
  HeartFilled,
  TrophyOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import Tilt from 'react-parallax-tilt';
import CountUp from 'react-countup';
import { BRAND } from '../../utils/constants';

const { Title, Paragraph, Text } = Typography;

const CTA = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99]
      }
    }
  };

  const stats = [
    { title: 'Projects Completed', value: 500, suffix: '+', icon: <RocketOutlined />, color: '#0057ff' },
    { title: 'Happy Clients', value: 250, suffix: '+', icon: <HeartFilled />, color: '#ef4444' },
    { title: 'Success Rate', value: 98, suffix: '%', icon: <TrophyOutlined />, color: '#f59e0b' },
    { title: 'Team Members', value: 25, suffix: '+', icon: <StarFilled />, color: '#10b981' }
  ];

  return (
    <section
      ref={ref}
      className="relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0057ff 0%, #1890ff 50%, #40a9ff 100%)',
        padding: '120px 0'
      }}
    >
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-20 left-20 w-80 h-80 rounded-full opacity-10"
          style={{
            background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 50%, transparent 100%)',
            filter: 'blur(60px)'
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-20 right-20 w-60 h-60 rounded-full opacity-10"
          style={{
            background: 'radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 50%, transparent 100%)',
            filter: 'blur(50px)'
          }}
          animate={{
            y: [0, 25, 0],
            x: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Floating icons */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              fontSize: '24px',
              color: 'white'
            }}
            animate={{
              y: [0, -30, 0],
              rotate: [0, 360],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut"
            }}
          >
            {i % 4 === 0 ? '🚀' : i % 4 === 1 ? '⭐' : i % 4 === 2 ? '💼' : '📈'}
          </motion.div>
        ))}

        {/* Grid pattern overlay */}
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      <div className="container-custom relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Main CTA Section */}
          <motion.div
            variants={itemVariants}
            className="text-center text-white mb-16"
          >
            {/* Badge */}
            <motion.div
              className="inline-block mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Badge.Ribbon
                text={
                  <span className="flex items-center gap-1">
                    <CrownOutlined className="text-yellow-300" />
                    <span className="font-semibold">Ready to Scale?</span>
                  </span>
                }
                color="rgba(255, 255, 255, 0.2)"
              >
                <Card
                  className="glass border-0"
                  style={{
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '16px',
                    padding: '12px 24px',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <Space align="center">
                    <motion.div
                      animate={{
                        rotate: [0, 360]
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    >
                      <GlobalOutlined className="text-white text-xl" />
                    </motion.div>
                    <span className="font-semibold text-white text-lg">Let's Transform Your Business</span>
                  </Space>
                </Card>
              </Badge.Ribbon>
            </motion.div>

            <Title
              level={1}
              className="text-white mb-8"
              style={{
                fontSize: 'clamp(2.5rem, 6vw, 4.5rem)',
                lineHeight: 1.1,
                fontWeight: 800,
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
              }}
            >
              Ready to{' '}
              <motion.span
                className="inline-block"
                animate={{
                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "linear"
                }}
                style={{
                  background: 'linear-gradient(90deg, #ffffff 25%, #e0f2fe 50%, #ffffff 75%)',
                  backgroundSize: '200% 100%',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                Dominate
              </motion.span>
              <br />
              Your Market?
            </Title>

            <Paragraph
              className="text-white/90 mb-12 max-w-4xl mx-auto"
              style={{
                fontSize: '24px',
                lineHeight: 1.6,
                fontWeight: 400
              }}
            >
              Take the first step towards{' '}
              <span className="font-semibold text-yellow-300">digital dominance</span>.
              Get a free consultation and discover how we can help your business{' '}
              <span className="font-semibold text-green-300">grow by 300%+</span>{' '}
              and leave your competition behind.
            </Paragraph>

            {/* Trust indicators */}
            <motion.div
              className="flex justify-center items-center gap-8 mb-12"
              variants={itemVariants}
            >
              {[
                { icon: <CheckCircleOutlined />, text: "Free Strategy Session", color: "#10b981" },
                { icon: <ThunderboltOutlined />, text: "Results in 30 Days", color: "#f59e0b" },
                { icon: <SafetyOutlined />, text: "Money-Back Guarantee", color: "#ef4444" }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-2 px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm border border-white/20"
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: 'rgba(255, 255, 255, 0.15)'
                  }}
                >
                  <span style={{ color: item.color, fontSize: '16px' }}>
                    {item.icon}
                  </span>
                  <span className="font-medium text-white text-sm">{item.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Enhanced CTA Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
          >
            {/* Primary CTA */}
            <motion.div
              whileHover={{ scale: 1.05, y: -4 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Link to="/contact">
                <Button
                  type="primary"
                  size="large"
                  icon={<RocketOutlined />}
                  className="h-16 px-12 rounded-2xl font-bold text-xl shadow-2xl"
                  style={{
                    background: 'white',
                    color: '#0057ff',
                    border: 'none',
                    minWidth: '280px'
                  }}
                >
                  <span className="flex items-center gap-3">
                    Get Free Strategy Session
                    <motion.span
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      →
                    </motion.span>
                  </span>
                </Button>
              </Link>
            </motion.div>

            {/* Secondary CTA */}
            <motion.div
              whileHover={{ scale: 1.05, y: -4 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <a href={`tel:${BRAND.phone}`}>
                <Button
                  size="large"
                  icon={<PhoneOutlined />}
                  className="h-16 px-12 rounded-2xl font-bold text-xl glass"
                  style={{
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(20px)',
                    color: 'white',
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                    minWidth: '280px'
                  }}
                >
                  Call Now - Free Consultation
                </Button>
              </a>
            </motion.div>
          </motion.div>

          {/* Stats Section */}
          <motion.div
            variants={itemVariants}
            className="mb-16"
          >
            <Row gutter={[32, 32]} justify="center">
              {stats.map((stat, index) => (
                <Col xs={12} sm={6} key={index}>
                  <Tilt
                    tiltMaxAngleX={5}
                    tiltMaxAngleY={5}
                    perspective={1000}
                    scale={1.02}
                  >
                    <motion.div
                      whileHover={{ y: -8 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Card
                        className="text-center border-0 glass"
                        style={{
                          background: 'rgba(255, 255, 255, 0.15)',
                          backdropFilter: 'blur(20px)',
                          borderRadius: '20px',
                          border: '1px solid rgba(255, 255, 255, 0.2)'
                        }}
                        bodyStyle={{ padding: '24px 16px' }}
                      >
                        <motion.div
                          className="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4"
                          style={{
                            background: 'rgba(255, 255, 255, 0.2)',
                            color: 'white'
                          }}
                          whileHover={{ rotate: 360, scale: 1.1 }}
                          transition={{ duration: 0.5 }}
                        >
                          <span className="text-2xl">{stat.icon}</span>
                        </motion.div>

                        <div className="text-3xl font-bold text-white mb-2">
                          {isInView && (
                            <CountUp
                              end={stat.value}
                              duration={2.5}
                              delay={index * 0.2}
                            />
                          )}
                          {stat.suffix}
                        </div>

                        <div className="text-white/80 font-medium">
                          {stat.title}
                        </div>
                      </Card>
                    </motion.div>
                  </Tilt>
                </Col>
              ))}
            </Row>
          </motion.div>

          {/* Enhanced Contact Info */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col lg:flex-row gap-8 justify-center items-center"
          >
            <motion.div
              whileHover={{ scale: 1.05, y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <a href={`tel:${BRAND.phone}`}>
                <Card
                  className="glass border-0 hover-lift"
                  style={{
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '20px',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                      <PhoneOutlined className="text-white text-2xl" />
                    </div>
                    <div>
                      <div className="text-white/80 text-sm font-medium mb-1">Call us directly</div>
                      <div className="text-white text-xl font-bold">{BRAND.phone}</div>
                      <div className="text-green-300 text-sm">Available 24/7</div>
                    </div>
                  </div>
                </Card>
              </a>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <a href={`mailto:${BRAND.email}`}>
                <Card
                  className="glass border-0 hover-lift"
                  style={{
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '20px',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                      <MailOutlined className="text-white text-2xl" />
                    </div>
                    <div>
                      <div className="text-white/80 text-sm font-medium mb-1">Send us an email</div>
                      <div className="text-white text-xl font-bold">{BRAND.email}</div>
                      <div className="text-blue-300 text-sm">Quick response</div>
                    </div>
                  </div>
                </Card>
              </a>
            </motion.div>
          </motion.div>

          {/* Guarantee */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-12 pt-8 border-t border-blue-400 border-opacity-30"
          >
            <p className="text-blue-100 text-lg">
              🚀 <strong>Free consultation</strong> • 📈 <strong>No long-term contracts</strong> • ⚡ <strong>Quick response time</strong>
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
