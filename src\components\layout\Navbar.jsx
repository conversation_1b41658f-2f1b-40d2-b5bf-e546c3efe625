import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { <PERSON><PERSON>, Drawer, Space, Badge, Dropdown, Avatar, Tooltip } from 'antd';
import {
  MenuOutlined,
  PhoneOutlined,
  RocketOutlined,
  StarFilled,
  DownOutlined,
  GlobalOutlined,
  MailOutlined,
  CloseOutlined,
  ThunderboltOutlined,
  FireOutlined,
  CrownOutlined,
  HeartFilled,
  SearchOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DashboardOutlined,
  TeamOutlined,
  TrophyOutlined,
  GiftOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_LINKS, BRAND } from '../../utils/constants';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  // Modern navigation items with enhanced styling
  const navItems = [
    { path: '/', label: 'Home', icon: '🏠', badge: null },
    { path: '/about', label: 'About', icon: '👥', badge: null },
    { 
      path: '/services', 
      label: 'Services', 
      icon: '⚡', 
      badge: 'Hot',
      dropdown: [
        { label: 'SEO & Marketing', path: '/services', icon: '📈' },
        { label: 'Web Development', path: '/services', icon: '💻' },
        { label: 'Social Media', path: '/services', icon: '📱' },
        { label: 'PPC Advertising', path: '/services', icon: '🎯' }
      ]
    },
    { path: '/case-studies', label: 'Portfolio', icon: '🏆', badge: null },
    { path: '/blog', label: 'Blog', icon: '📝', badge: 'New' },
    { path: '/contact', label: 'Contact', icon: '💬', badge: null }
  ];

  // User menu items
  const userMenuItems = {
    items: [
      {
        key: 'dashboard',
        label: (
          <div className="flex items-center gap-3 p-2">
            <DashboardOutlined className="text-blue-500" />
            <span>Dashboard</span>
          </div>
        ),
      },
      {
        key: 'profile',
        label: (
          <div className="flex items-center gap-3 p-2">
            <UserOutlined className="text-green-500" />
            <span>Profile</span>
          </div>
        ),
      },
      {
        key: 'settings',
        label: (
          <div className="flex items-center gap-3 p-2">
            <SettingOutlined className="text-gray-500" />
            <span>Settings</span>
          </div>
        ),
      },
      {
        type: 'divider',
      },
      {
        key: 'logout',
        label: (
          <div className="flex items-center gap-3 p-2 text-red-500">
            <LogoutOutlined />
            <span>Logout</span>
          </div>
        ),
      },
    ],
  };

  return (
    <motion.nav
      className="fixed top-0 left-0 right-0 z-50"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      {/* Main Navbar */}
      <div
        className={`transition-all duration-500 ${
          isScrolled 
            ? 'bg-white/95 backdrop-blur-xl shadow-2xl border-b border-gray-100' 
            : 'bg-white/90 backdrop-blur-lg shadow-lg'
        }`}
        style={{
          borderBottom: isScrolled ? '1px solid rgba(0, 0, 0, 0.05)' : 'none'
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            
            {/* Logo Section */}
            <motion.div
              className="flex items-center gap-4"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Link to="/" className="flex items-center gap-4" onClick={closeMenu}>
                {/* Enhanced Logo */}
                <div className="relative">
                  <motion.div
                    className="w-16 h-16 rounded-3xl flex items-center justify-center relative overflow-hidden"
                    style={{ 
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      boxShadow: '0 10px 30px rgba(102, 126, 234, 0.3)'
                    }}
                    whileHover={{
                      boxShadow: '0 15px 40px rgba(102, 126, 234, 0.4)',
                      y: -2,
                      rotate: [0, -2, 2, 0]
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <span className="text-white font-bold text-2xl relative z-10">B</span>
                    
                    {/* Animated shine effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0"
                      whileHover={{ 
                        opacity: 0.2, 
                        x: ['-100%', '100%'] 
                      }}
                      transition={{ duration: 0.6 }}
                    />
                    
                    {/* Floating particles */}
                    <motion.div
                      className="absolute top-2 right-2 w-2 h-2 bg-yellow-300 rounded-full"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.7, 1, 0.7],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                  </motion.div>
                  
                  {/* Status indicator */}
                  <motion.div
                    className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center"
                    animate={{
                      scale: [1, 1.2, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                    }}
                  >
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </motion.div>
                </div>
                
                {/* Brand Text */}
                <div className="flex flex-col">
                  <motion.span 
                    className="text-2xl font-bold"
                    style={{ 
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}
                    whileHover={{ scale: 1.05 }}
                  >
                    {BRAND.name}
                  </motion.span>
                  <span className="text-xs text-gray-500 font-medium -mt-1">
                    Digital Excellence
                  </span>
                </div>
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center gap-2">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                  whileHover={{ y: -2 }}
                >
                  {item.dropdown ? (
                    <Dropdown
                      menu={{
                        items: item.dropdown.map((dropItem, idx) => ({
                          key: idx,
                          label: (
                            <Link to={dropItem.path} className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                              <span className="text-xl">{dropItem.icon}</span>
                              <div>
                                <div className="font-semibold text-gray-800">{dropItem.label}</div>
                                <div className="text-xs text-gray-500">Professional services</div>
                              </div>
                            </Link>
                          ),
                        }))
                      }}
                      trigger={['hover']}
                      placement="bottomLeft"
                    >
                      <Link
                        to={item.path}
                        className={`relative flex items-center gap-2 px-4 py-3 rounded-2xl font-medium transition-all duration-300 group ${
                          location.pathname === item.path
                            ? 'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-600 shadow-lg'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        <span className="text-lg">{item.icon}</span>
                        <span>{item.label}</span>
                        <DownOutlined className="text-xs opacity-60" />
                        {item.badge && (
                          <Badge 
                            count={item.badge} 
                            style={{ 
                              backgroundColor: '#ff4d4f',
                              fontSize: '10px',
                              height: '18px',
                              lineHeight: '18px',
                              minWidth: '18px'
                            }}
                          />
                        )}
                      </Link>
                    </Dropdown>
                  ) : (
                    <Link
                      to={item.path}
                      className={`relative flex items-center gap-2 px-4 py-3 rounded-2xl font-medium transition-all duration-300 group ${
                        location.pathname === item.path
                          ? 'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-600 shadow-lg'
                          : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                      }`}
                    >
                      <span className="text-lg">{item.icon}</span>
                      <span>{item.label}</span>
                      {item.badge && (
                        <Badge 
                          count={item.badge} 
                          style={{ 
                            backgroundColor: item.badge === 'Hot' ? '#ff4d4f' : '#52c41a',
                            fontSize: '10px',
                            height: '18px',
                            lineHeight: '18px',
                            minWidth: '18px'
                          }}
                        />
                      )}
                      
                      {/* Hover effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-2xl opacity-0 group-hover:opacity-100"
                        transition={{ duration: 0.3 }}
                      />
                    </Link>
                  )}
                </motion.div>
              ))}
            </div>

            {/* Action Buttons & User Menu */}
            <div className="hidden lg:flex items-center gap-4">
              {/* Search Button */}
              <Tooltip title="Search">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <Button
                    type="text"
                    icon={<SearchOutlined />}
                    className="w-12 h-12 rounded-2xl flex items-center justify-center hover:bg-gray-100 transition-colors"
                    onClick={() => setSearchOpen(!searchOpen)}
                  />
                </motion.div>
              </Tooltip>

              {/* Notifications */}
              <Tooltip title="Notifications">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <Badge count={3} size="small">
                    <Button
                      type="text"
                      icon={<BellOutlined />}
                      className="w-12 h-12 rounded-2xl flex items-center justify-center hover:bg-gray-100 transition-colors"
                    />
                  </Badge>
                </motion.div>
              </Tooltip>

              {/* Contact Button */}
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <a href={`tel:${BRAND.phone}`}>
                  <Button
                    type="text"
                    icon={<PhoneOutlined />}
                    className="flex items-center gap-2 px-4 py-2 h-12 rounded-2xl font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300"
                  >
                    <span className="hidden xl:inline">Call</span>
                  </Button>
                </a>
              </motion.div>

              {/* CTA Button */}
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <Link to="/contact">
                  <Button
                    type="primary"
                    icon={<RocketOutlined />}
                    className="h-12 px-6 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none',
                      fontSize: '15px'
                    }}
                  >
                    Get Started
                  </Button>
                </Link>
              </motion.div>

              {/* User Avatar */}
              <Dropdown menu={userMenuItems} trigger={['click']} placement="bottomRight">
                <motion.div whileHover={{ scale: 1.05 }} className="cursor-pointer">
                  <Badge dot status="success">
                    <Avatar
                      size={48}
                      style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        border: '2px solid white',
                        boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)'
                      }}
                      icon={<UserOutlined />}
                    />
                  </Badge>
                </motion.div>
              </Dropdown>
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  type="text"
                  onClick={toggleMenu}
                  className="w-12 h-12 rounded-2xl flex items-center justify-center hover:bg-gray-100 transition-all duration-300"
                  style={{
                    color: isOpen ? '#667eea' : '#374151',
                    background: isOpen ? 'rgba(102, 126, 234, 0.1)' : 'transparent'
                  }}
                >
                  <motion.div
                    animate={{ rotate: isOpen ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    {isOpen ? <CloseOutlined className="text-lg" /> : <MenuOutlined className="text-lg" />}
                  </motion.div>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Drawer */}
      <Drawer
        title={
          <div className="flex items-center gap-3">
            <div
              className="w-12 h-12 rounded-2xl flex items-center justify-center"
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)'
              }}
            >
              <span className="text-white font-bold text-xl">B</span>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-900">{BRAND.name}</div>
              <div className="text-sm text-gray-500">Digital Excellence</div>
            </div>
          </div>
        }
        placement="right"
        onClose={closeMenu}
        open={isOpen}
        width={380}
        styles={{
          body: {
            padding: 0,
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
          },
          header: {
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
            padding: '20px 24px'
          }
        }}
        closeIcon={<CloseOutlined className="text-gray-500 hover:text-gray-700" />}
      >
        <div className="flex flex-col h-full">
          {/* Navigation Links */}
          <div className="flex-1 p-6">
            <div className="space-y-2 mb-8">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    to={item.path}
                    onClick={closeMenu}
                    className={`flex items-center gap-4 px-4 py-4 rounded-2xl text-lg font-medium transition-all duration-300 ${
                      location.pathname === item.path
                        ? 'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-600 shadow-lg border border-blue-100'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                    }`}
                  >
                    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center text-2xl ${
                      location.pathname === item.path
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-500'
                    }`}>
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span>{item.label}</span>
                        {item.badge && (
                          <Badge
                            count={item.badge}
                            style={{
                              backgroundColor: item.badge === 'Hot' ? '#ff4d4f' : '#52c41a',
                              fontSize: '10px'
                            }}
                          />
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Quick Actions */}
            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <Link to="/contact" onClick={closeMenu}>
                <Button
                  type="primary"
                  block
                  size="large"
                  icon={<RocketOutlined />}
                  className="h-14 rounded-2xl font-semibold text-lg shadow-lg"
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none'
                  }}
                >
                  Get Started
                </Button>
              </Link>

              <Button
                block
                size="large"
                icon={<PhoneOutlined />}
                className="h-14 rounded-2xl font-semibold text-lg border-2 border-purple-200 text-purple-600 hover:bg-purple-50"
              >
                Call Now
              </Button>
            </motion.div>
          </div>

          {/* Footer Section */}
          <motion.div
            className="p-6 bg-gray-50 border-t border-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="text-sm font-semibold text-gray-900">Quick Contact</div>
                <div className="text-xs text-gray-500">Get in touch with us</div>
              </div>
              <div className="flex gap-2">
                <Button
                  type="text"
                  icon={<PhoneOutlined />}
                  className="w-10 h-10 rounded-xl bg-blue-100 text-blue-600 hover:bg-blue-200"
                />
                <Button
                  type="text"
                  icon={<MailOutlined />}
                  className="w-10 h-10 rounded-xl bg-green-100 text-green-600 hover:bg-green-200"
                />
              </div>
            </div>

            <div className="space-y-1">
              <div className="text-sm text-gray-600">{BRAND.phone}</div>
              <div className="text-sm text-gray-600">{BRAND.email}</div>
            </div>
          </motion.div>
        </div>
      </Drawer>
    </motion.nav>
  );
};

export default Navbar;
