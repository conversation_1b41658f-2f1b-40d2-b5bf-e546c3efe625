import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Search, Share2, Target, Palette, Monitor, PenTool, ArrowRight, CheckCircle } from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import CTA from '../components/sections/CTA';
import { SERVICES } from '../utils/constants';

const Services = () => {
  useEffect(() => {
    document.title = 'Our Services - Brandify | Digital Marketing Solutions';
    document.querySelector('meta[name="description"]')?.setAttribute('content', 
      'Explore Brandify\'s comprehensive digital marketing services including SEO, social media marketing, PPC advertising, branding, web development, and content marketing.'
    );
  }, []);

  const getIcon = (iconName) => {
    const icons = {
      Search,
      Share2,
      Target,
      Palette,
      Monitor,
      PenTool
    };
    const IconComponent = icons[iconName];
    return IconComponent ? <IconComponent size={48} /> : <Search size={48} />;
  };

  const processSteps = [
    {
      step: '01',
      title: 'Discovery & Analysis',
      description: 'We analyze your business, competitors, and target audience to create a tailored strategy.'
    },
    {
      step: '02',
      title: 'Strategy Development',
      description: 'Our experts develop a comprehensive digital marketing plan aligned with your goals.'
    },
    {
      step: '03',
      title: 'Implementation',
      description: 'We execute the strategy with precision, monitoring every detail for optimal performance.'
    },
    {
      step: '04',
      title: 'Optimization & Reporting',
      description: 'Continuous optimization based on data insights and regular performance reporting.'
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Our Digital Marketing Services
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Comprehensive digital marketing solutions designed to grow your business, increase brand visibility, and drive measurable results.
            </p>
            <Link to="/contact">
              <Button size="xl" className="group">
                Get Started Today
                <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-20">
            {SERVICES.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                }`}
              >
                {/* Content */}
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mr-4">
                      {getIcon(service.icon)}
                    </div>
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                      {service.title}
                    </h2>
                  </div>
                  
                  <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                    {service.description}
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    {service.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center space-x-3">
                        <CheckCircle size={20} className="text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{benefit}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Link to="/contact">
                    <Button className="group">
                      Learn More
                      <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>

                {/* Image */}
                <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                  <Card className="overflow-hidden" padding="none">
                    <img
                      src={`https://images.unsplash.com/photo-${
                        index === 0 ? '1432888622747-4eb9a8efeb07' :
                        index === 1 ? '1611224923853-80b023f02d71' :
                        index === 2 ? '1460925895917-afdab827c52f' :
                        index === 3 ? '1558655146-364adaf25c9d' :
                        index === 4 ? '1467232004584-a241de8bcf5d' :
                        '1552664730-d307ca884978'
                      }?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                      alt={service.title}
                      className="w-full h-80 object-cover"
                    />
                  </Card>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Process</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We follow a proven methodology to ensure your digital marketing campaigns deliver exceptional results.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center h-full relative" hover={true}>
                  <div className="text-6xl font-bold text-primary opacity-20 mb-4">
                    {step.step}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                  {index < processSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                      <ArrowRight size={24} className="text-primary" />
                    </div>
                  )}
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Why Choose Brandify?</h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full" hover={true}>
                <div className="text-4xl font-bold text-primary mb-4">500+</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Successful Projects</h3>
                <p className="text-gray-600">
                  We've delivered outstanding results for over 500 businesses across various industries.
                </p>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full" hover={true}>
                <div className="text-4xl font-bold text-primary mb-4">98%</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Client Satisfaction</h3>
                <p className="text-gray-600">
                  Our commitment to excellence has earned us a 98% client satisfaction rate.
                </p>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full" hover={true}>
                <div className="text-4xl font-bold text-primary mb-4">24/7</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Support Available</h3>
                <p className="text-gray-600">
                  Our dedicated support team is available around the clock to assist you.
                </p>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      <CTA />
    </div>
  );
};

export default Services;
