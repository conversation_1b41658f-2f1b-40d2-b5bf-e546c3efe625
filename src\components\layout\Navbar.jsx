import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { <PERSON><PERSON>, Drawer, Badge, Dropdown } from 'antd';
import {
  MenuOutlined,
  PhoneOutlined,
  RocketOutlined,
  DownOutlined,
  MailOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { NAV_LINKS, BRAND } from '../../utils/constants';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  const navItems = [
    { path: '/', label: 'Home', icon: '🏠' },
    { path: '/about', label: 'About', icon: '👥' },
    { 
      path: '/services', 
      label: 'Services', 
      icon: '⚡', 
      badge: 'Hot',
      dropdown: [
        { label: 'SEO & Marketing', path: '/services', icon: '📈' },
        { label: 'Web Development', path: '/services', icon: '💻' },
        { label: 'Social Media', path: '/services', icon: '📱' },
        { label: 'PPC Advertising', path: '/services', icon: '🎯' }
      ]
    },
    { path: '/case-studies', label: 'Portfolio', icon: '🏆' },
    { path: '/blog', label: 'Blog', icon: '📝', badge: 'New' },
    { path: '/contact', label: 'Contact', icon: '💬' }
  ];

  return (
    <motion.nav
      className="fixed top-0 left-0 right-0 z-50 w-full"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div
        className={`w-full transition-all duration-500 navbar-enhanced ${
          isScrolled ? 'scrolled' : ''
        }`}
      >
        {/* Desktop Navigation */}
        <div className="hidden lg:block w-full">
          <div className="w-full px-8 xl:px-12">
            <div className="flex items-center h-32">
              
              {/* Logo Section */}
              <div className="flex items-center w-96">
                <motion.div
                  className="flex items-center"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link to="/" className="flex items-center" onClick={closeMenu}>
                    <motion.div
                      className="flex flex-col"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      <motion.span
                        className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent"
                        style={{
                          fontFamily: 'Inter, system-ui, sans-serif',
                          letterSpacing: '-0.02em'
                        }}
                      >
                        {BRAND.name}
                      </motion.span>
                      <motion.span
                        className="text-sm text-gray-600 font-semibold -mt-1 flex items-center gap-1"
                        animate={{
                          opacity: [0.7, 1, 0.7]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity
                        }}
                      >
                        <span className="text-xs">✨</span>
                        Digital Excellence
                        <span className="text-xs">🚀</span>
                      </motion.span>
                    </motion.div>
                  </Link>
                </motion.div>
              </div>

              {/* Center Navigation */}
              <div className="flex items-center justify-center flex-1 px-10">
                <div className="flex items-center gap-3 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-2 shadow-lg border border-white/50">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.path}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                      whileHover={{ y: -2, scale: 1.05 }}
                    >
                      {item.dropdown ? (
                        <Dropdown
                          menu={{
                            items: item.dropdown.map((dropItem, idx) => ({
                              key: idx,
                              label: (
                                <Link to={dropItem.path} className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                  <span className="text-lg">{dropItem.icon}</span>
                                  <div>
                                    <div className="font-semibold text-gray-800">{dropItem.label}</div>
                                    <div className="text-xs text-gray-500">Professional services</div>
                                  </div>
                                </Link>
                              ),
                            }))
                          }}
                          trigger={['hover']}
                          placement="bottomCenter"
                        >
                          <Link
                            to={item.path}
                            className={`relative flex items-center gap-3 px-5 py-3 rounded-xl font-semibold transition-all duration-300 group ${
                              location.pathname === item.path
                                ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white shadow-lg transform scale-105'
                                : 'text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:via-purple-500 hover:to-pink-500 hover:shadow-lg hover:scale-105'
                            }`}
                          >
                            <span className="text-sm">{item.icon}</span>
                            <span className="text-sm font-semibold">{item.label}</span>
                            <DownOutlined className="text-xs opacity-60" />
                            {item.badge && (
                              <Badge 
                                count={item.badge} 
                                style={{ 
                                  backgroundColor: '#ef4444',
                                  fontSize: '8px',
                                  height: '14px',
                                  lineHeight: '14px',
                                  minWidth: '14px'
                                }}
                              />
                            )}
                          </Link>
                        </Dropdown>
                      ) : (
                        <Link
                          to={item.path}
                          className={`relative flex items-center gap-3 px-5 py-3 rounded-xl font-semibold transition-all duration-300 group ${
                            location.pathname === item.path
                              ? 'bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white shadow-lg transform scale-105'
                              : 'text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:via-purple-500 hover:to-pink-500 hover:shadow-lg hover:scale-105'
                          }`}
                        >
                          <span className="text-sm">{item.icon}</span>
                          <span className="text-sm font-semibold">{item.label}</span>
                          {item.badge && (
                            <Badge 
                              count={item.badge} 
                              style={{ 
                                backgroundColor: item.badge === 'Hot' ? '#ef4444' : '#10b981',
                                fontSize: '8px',
                                height: '14px',
                                lineHeight: '14px',
                                minWidth: '14px'
                              }}
                            />
                          )}
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Right Action Button */}
              <div className="flex items-center justify-end w-96">
                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link to="/contact">
                    <Button
                      type="primary"
                      icon={<RocketOutlined className="text-lg" />}
                      className="h-14 px-8 rounded-2xl font-bold shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center gap-3 text-lg"
                      style={{
                        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                        border: 'none',
                        fontSize: '16px'
                      }}
                    >
                      🚀 Get Started
                    </Button>
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden w-full">
          <div className="px-6 sm:px-8">
            <div className="flex items-center justify-between h-20">
              
              <motion.div
                className="flex items-center"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Link to="/" className="flex items-center" onClick={closeMenu}>
                  <motion.div
                    className="flex flex-col"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <span className="text-xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                      {BRAND.name}
                    </span>
                    <span className="text-xs text-gray-600 font-semibold -mt-0.5 flex items-center gap-1">
                      <span className="text-xs">✨</span>
                      Digital Excellence
                    </span>
                  </motion.div>
                </Link>
              </motion.div>

              <div className="flex items-center gap-4">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link to="/contact">
                    <Button
                      type="primary"
                      size="middle"
                      icon={<RocketOutlined />}
                      className="h-10 px-4 rounded-xl font-bold text-sm shadow-lg"
                      style={{
                        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                        border: 'none'
                      }}
                    >
                      🚀 Start
                    </Button>
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    type="text"
                    onClick={toggleMenu}
                    className="w-9 h-9 rounded-lg flex items-center justify-center hover:bg-gray-100 transition-all duration-300"
                    style={{ 
                      color: isOpen ? '#1e40af' : '#374151',
                      background: isOpen ? 'rgba(30, 64, 175, 0.1)' : 'transparent'
                    }}
                  >
                    <motion.div
                      animate={{ rotate: isOpen ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {isOpen ? <CloseOutlined className="text-sm" /> : <MenuOutlined className="text-sm" />}
                    </motion.div>
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Drawer */}
      <Drawer
        title={
          <div className="flex items-center">
            <div>
              <div className="text-xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                {BRAND.name}
              </div>
              <div className="text-sm text-gray-600 font-semibold flex items-center gap-1">
                <span className="text-xs">✨</span>
                Digital Excellence
                <span className="text-xs">🚀</span>
              </div>
            </div>
          </div>
        }
        placement="right"
        onClose={closeMenu}
        open={isOpen}
        width={320}
        styles={{
          body: {
            padding: 0,
            background: 'linear-gradient(135deg, #ffffff 0%, #f9fafb 100%)'
          },
          header: {
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            padding: '16px 20px'
          }
        }}
        closeIcon={<CloseOutlined className="text-gray-500 hover:text-gray-700" />}
      >
        <div className="flex flex-col h-full">
          <div className="flex-1 p-5">
            <div className="space-y-1 mb-6">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    to={item.path}
                    onClick={closeMenu}
                    className={`flex items-center gap-3 px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 ${
                      location.pathname === item.path
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                    }`}
                  >
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-lg ${
                      location.pathname === item.path
                        ? 'bg-white/20 text-white'
                        : 'bg-gray-100 text-gray-500'
                    }`}>
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span>{item.label}</span>
                        {item.badge && (
                          <Badge
                            count={item.badge}
                            style={{
                              backgroundColor: item.badge === 'Hot' ? '#ef4444' : '#10b981',
                              fontSize: '9px',
                              height: '16px',
                              lineHeight: '16px'
                            }}
                          />
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>

            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <Link to="/contact" onClick={closeMenu}>
                <Button
                  type="primary"
                  block
                  size="large"
                  icon={<RocketOutlined />}
                  className="h-14 rounded-2xl font-bold text-lg shadow-xl"
                  style={{
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                    border: 'none'
                  }}
                >
                  🚀 Get Started Now
                </Button>
              </Link>
            </motion.div>
          </div>

          <motion.div
            className="p-5 bg-gray-50 border-t border-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="text-sm font-semibold text-gray-900">📧 Email Contact</div>
                <div className="text-xs text-gray-500">Send us a message</div>
              </div>
              <div className="flex gap-2">
                <a href={`mailto:${BRAND.email}`}>
                  <Button
                    type="text"
                    icon={<MailOutlined />}
                    className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-100 to-purple-100 text-purple-600 hover:from-purple-200 hover:to-pink-200"
                  />
                </a>
              </div>
            </div>

            <div className="space-y-1">
              <div className="text-sm text-gray-600 font-medium">{BRAND.email}</div>
            </div>
          </motion.div>
        </div>
      </Drawer>
    </motion.nav>
  );
};

export default Navbar;
